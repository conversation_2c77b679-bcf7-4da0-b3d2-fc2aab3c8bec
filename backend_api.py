import os
print(f"Current PID: {os.getpid()}")
print("Initializing Hybrid Search API server...", flush=True)
print("Loading dependencies, please wait...", flush=True)

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, field_validator, model_validator
from sentence_transformers import SentenceTransformer
from sqlalchemy import create_engine, text, exc
import connectorx as cx
import logging
import time
import traceback
from typing import Optional, List, Dict, Any
import uvicorn
from contextlib import asynccontextmanager
import numpy as np
import re
from collections import Counter
import math
import psutil
import gc
import pickle
import joblib
from scipy.sparse import csr_matrix


# Configuration for destination DB
srv_name2 = "HQDCSMOSQL01"
db_name2 = "PA_DEV2"
usr_name2 = "PAadmin"
pw2 = "PAadmin"

query_table = "ReportNLP"


# Connection URL for destination DB
connection_url2 = (
    "mssql+pyodbc://"
    f"{usr_name2}:{pw2}@{srv_name2}/{db_name2}"
    "?driver=ODBC+Driver+17+for+SQL+Server"
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('search_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Global variables for model and database
model = None
engine = None
search_cache = None

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def force_garbage_collection():
    """Force garbage collection and return memory freed"""
    before = get_memory_usage()
    gc.collect()
    after = get_memory_usage()
    return before - after

class DiskBackedBM25:
    """
    Disk-backed BM25 implementation for large document collections
    Stores term frequencies and vocabulary on disk to minimize RAM usage
    """
    def __init__(self, cache_dir: str = "search_cache", k1: float = 1.5, b: float = 0.75):
        self.cache_dir = cache_dir
        self.k1 = k1
        self.b = b
        self.doc_count = 0
        self.avg_doc_length = 0
        self.vocab_file = f"{cache_dir}/bm25_vocab.pkl"
        self.doc_lengths_file = f"{cache_dir}/bm25_doc_lengths.pkl"
        self.term_doc_freq_file = f"{cache_dir}/bm25_term_doc_freq.pkl"

        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)

    def build_from_text_chunks(self, text_chunks_generator):
        """Build BM25 index from text chunks, processing incrementally"""
        logger.info("Building disk-backed BM25 index...")

        vocab_set = set()
        doc_lengths = []
        term_doc_freq = {}
        processed_docs = 0

        # Process chunks incrementally
        for chunk_idx, (ids, texts) in enumerate(text_chunks_generator):
            logger.info(f"Processing BM25 chunk {chunk_idx + 1}: {len(texts)} documents")

            chunk_doc_lengths = []
            chunk_vocab = set()

            # Process each document in the chunk
            for text in texts:
                if not text:
                    tokens = []
                else:
                    tokens = re.findall(r'\b\w+\b', text.lower())

                doc_length = len(tokens)
                chunk_doc_lengths.append(doc_length)

                # Update vocabulary and term document frequencies
                unique_tokens = set(tokens)
                chunk_vocab.update(unique_tokens)

                for token in unique_tokens:
                    term_doc_freq[token] = term_doc_freq.get(token, 0) + 1

            doc_lengths.extend(chunk_doc_lengths)
            vocab_set.update(chunk_vocab)
            processed_docs += len(texts)

            # Periodic cleanup
            if chunk_idx % 10 == 0:
                force_garbage_collection()

        self.doc_count = processed_docs
        self.avg_doc_length = sum(doc_lengths) / len(doc_lengths) if doc_lengths else 0

        # Save to disk
        logger.info(f"Saving BM25 index to disk: {len(vocab_set)} terms, {self.doc_count} documents")

        joblib.dump(list(vocab_set), self.vocab_file)
        joblib.dump(doc_lengths, self.doc_lengths_file)
        joblib.dump(term_doc_freq, self.term_doc_freq_file)

        logger.info("BM25 index saved to disk")
        force_garbage_collection()

    def load_from_disk(self):
        """Load BM25 index from disk"""
        logger.info("Loading BM25 index from disk...")

        self.vocab = set(joblib.load(self.vocab_file))
        self.doc_lengths = joblib.load(self.doc_lengths_file)
        self.term_doc_freq = joblib.load(self.term_doc_freq_file)

        self.doc_count = len(self.doc_lengths)
        self.avg_doc_length = sum(self.doc_lengths) / len(self.doc_lengths) if self.doc_lengths else 0

        logger.info(f"BM25 index loaded: {len(self.vocab)} terms, {self.doc_count} documents")

    def get_scores(self, query: str) -> np.ndarray:
        """Calculate BM25 scores for all documents given a query"""
        query_tokens = re.findall(r'\b\w+\b', query.lower())
        scores = np.zeros(self.doc_count, dtype=np.float32)

        unique_query_tokens = set(query_tokens)

        for token in unique_query_tokens:
            if token not in self.term_doc_freq:
                continue

            # Calculate IDF
            df = self.term_doc_freq[token]
            idf = math.log((self.doc_count - df + 0.5) / (df + 0.5))

            if idf <= 0:
                continue

            query_tf = query_tokens.count(token)

            # This is a simplified version - for full BM25, we'd need to store
            # term frequencies per document, which would require more disk storage
            # For now, we'll use a simplified scoring that works with our current data
            for doc_idx in range(self.doc_count):
                if self.doc_lengths[doc_idx] > 0:
                    # Simplified BM25 - assumes term frequency of 1 if term exists
                    # This is less accurate but much more memory efficient
                    doc_length = self.doc_lengths[doc_idx]
                    numerator = 1 * (self.k1 + 1)
                    denominator = 1 + self.k1 * (1 - self.b + self.b * (doc_length / self.avg_doc_length))
                    scores[doc_idx] += idf * (numerator / denominator) * query_tf

        return scores

def get_text_chunks_generator(chunk_size=50000):
    """Generator that yields text chunks for BM25 processing"""
    logger.info("Starting text chunk generation for BM25...")

    # Get total count first
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT COUNT(*) as total_count
            FROM ReportNLP
            WHERE empty_report = 0
              AND empty_embedding = 0
              AND extracted_text IS NOT NULL
              AND embedding_vector IS NOT NULL
        """))
        total_count = result.fetchone()[0]

    logger.info(f"Total documents for BM25: {total_count}")

    # Yield chunks
    for offset in range(0, total_count, chunk_size):
        logger.info(f"Loading text chunk: offset {offset}, size {chunk_size}")

        df_chunk = cx.read_sql(
            connection_url2,
            f"""
            SELECT Id, extracted_text
            FROM ReportNLP
            WHERE empty_report = 0
              AND empty_embedding = 0
              AND extracted_text IS NOT NULL
              AND embedding_vector IS NOT NULL
            ORDER BY Id
            OFFSET {offset} ROWS FETCH NEXT {chunk_size} ROWS ONLY
            """,
            return_type="pandas"
        )

        if df_chunk.empty:
            break

        ids = df_chunk['Id'].astype(str).tolist()
        texts = df_chunk['extracted_text'].tolist()

        yield ids, texts

        # Clean up chunk
        del df_chunk
        force_garbage_collection()

def load_embeddings_chunked(cache_dir="search_cache", chunk_size=50000):
    """Load embeddings in chunks and store in memory-mapped file"""
    logger.info("Loading embeddings with chunked processing...")

    # Get total count
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT COUNT(*) as total_count
            FROM ReportNLP
            WHERE empty_report = 0
              AND empty_embedding = 0
              AND extracted_text IS NOT NULL
              AND embedding_vector IS NOT NULL
        """))
        total_count = result.fetchone()[0]

    logger.info(f"Total records for embeddings: {total_count}")

    # Create memory-mapped files
    os.makedirs(cache_dir, exist_ok=True)

    # Initialize memory-mapped arrays (assuming 384-dim embeddings)
    embeddings_mmap = np.memmap(
        f"{cache_dir}/embeddings.dat",
        dtype=np.float16,
        mode='w+',
        shape=(total_count, 384)
    )

    ids_list = []
    processed = 0

    # Process in chunks
    for offset in range(0, total_count, chunk_size):
        logger.info(f"Processing embedding chunk: {offset//chunk_size + 1}, records {offset}-{min(offset+chunk_size, total_count)}")

        # Load chunk with ConnectorX
        df_chunk = cx.read_sql(
            connection_url2,
            f"""
            SELECT Id, embedding_vector
            FROM ReportNLP
            WHERE empty_report = 0
              AND empty_embedding = 0
              AND extracted_text IS NOT NULL
              AND embedding_vector IS NOT NULL
            ORDER BY Id
            OFFSET {offset} ROWS FETCH NEXT {chunk_size} ROWS ONLY
            """,
            return_type="pandas"
        )

        if df_chunk.empty:
            break

        # Process embeddings for this chunk
        chunk_ids = []
        valid_count = 0

        for i, (row_id, embedding_str) in enumerate(zip(df_chunk['Id'], df_chunk['embedding_vector'])):
            try:
                if embedding_str:
                    embedding = np.fromstring(embedding_str, sep=',', dtype=np.float32)
                    if len(embedding) == 384:  # Validate dimension
                        embeddings_mmap[processed + valid_count] = embedding.astype(np.float16)
                        chunk_ids.append(str(row_id))
                        valid_count += 1
            except (ValueError, AttributeError) as e:
                logger.warning(f"Failed to parse embedding for ID {row_id}: {e}")
                continue

        ids_list.extend(chunk_ids)
        processed += valid_count

        # Force cleanup after each chunk
        del df_chunk
        force_garbage_collection()

    # Resize mmap to actual processed count
    if processed < total_count:
        logger.info(f"Resizing embeddings array from {total_count} to {processed}")
        embeddings_mmap = np.memmap(
            f"{cache_dir}/embeddings.dat",
            dtype=np.float16,
            mode='r+',
            shape=(processed, 384)
        )

    # Save IDs to disk
    with open(f"{cache_dir}/ids.pkl", 'wb') as f:
        pickle.dump(ids_list, f)

    logger.info(f"Embeddings loading complete: {processed} records saved to {cache_dir}")

    return {
        'embeddings_file': f"{cache_dir}/embeddings.dat",
        'ids_file': f"{cache_dir}/ids.pkl",
        'count': processed,
        'shape': (processed, 384)
    }

def load_search_cache_chunked():
    """Load search data using chunked processing and disk-backed storage"""
    logger.info("Loading search cache with chunked processing...")

    try:
        start_time = time.time()
        cache_dir = "search_cache"

        # Step 1: Build BM25 index from text chunks
        logger.info("Building BM25 index...")
        bm25 = DiskBackedBM25(cache_dir)
        text_chunks = get_text_chunks_generator()
        bm25.build_from_text_chunks(text_chunks)

        # Step 2: Load embeddings in chunks
        logger.info("Loading embeddings...")
        embedding_info = load_embeddings_chunked(cache_dir)

        # Step 3: Load everything into memory-mapped format
        logger.info("Setting up memory-mapped access...")

        # Load embeddings as memory-mapped array
        embeddings_mmap = np.memmap(
            embedding_info['embeddings_file'],
            dtype=np.float16,
            mode='r',
            shape=embedding_info['shape']
        )

        # Load IDs
        with open(embedding_info['ids_file'], 'rb') as f:
            ids = pickle.load(f)

        # Load BM25 from disk
        bm25.load_from_disk()

        load_time = time.time() - start_time
        logger.info(f"Chunked cache loading completed in {load_time:.2f}s")
        logger.info(f"Loaded {len(ids)} records with memory-mapped storage")

        force_garbage_collection()

        return {
            'ids': np.array(ids),
            'embeddings': embeddings_mmap,  # Memory-mapped array
            'bm25': bm25,  # Disk-backed BM25
            'last_updated': time.time(),
            'cache_dir': cache_dir,
            'is_memory_mapped': True
        }

    except Exception as e:
        logger.error(f"Chunked cache loading failed: {str(e)}")
        # Fallback to original method if available
        logger.info("Falling back to SQLAlchemy method...")
        return load_search_cache_sqlalchemy()

def load_search_cache_sqlalchemy():
    """Fallback: Load search data into memory with SQLAlchemy"""
    logger.info("Loading search data into memory with SQLAlchemy (fallback)...")
    
    try:
        with engine.connect() as conn:
            # Use server-side cursor for large datasets
            result = conn.execution_options(stream_results=True).execute(text("""
                SELECT Id, extracted_text, embedding_vector
                FROM ReportNLP 
                WHERE empty_report = 0 
                  AND empty_embedding = 0
                  AND extracted_text IS NOT NULL
                  AND embedding_vector IS NOT NULL
                  AND LEN(extracted_text) > 0
                  AND LEN(embedding_vector) > 0
                ORDER BY Id
            """))
            
            ids = []
            texts = []
            embeddings = []
            
            logger.info("Processing records...")
            batch_size = 5000  # Smaller batches for better memory control
            processed = 0
            
            for row in result:
                if processed % batch_size == 0:
                    current_mem = get_memory_usage()
                    logger.info(f"Processed {processed} records. Memory: {current_mem:.1f}MB")
                    
                    # Force garbage collection every batch
                    if processed > 0:
                        freed = force_garbage_collection()
                        if freed > 5:
                            logger.info(f"GC freed {freed:.1f}MB")
                
                ids.append(str(row.Id))
                texts.append(row.extracted_text)
                
                # Parse embedding with error handling
                try:
                    # More memory-efficient parsing
                    embedding_str = row.embedding_vector
                    if not embedding_str:
                        raise ValueError("Empty embedding")
                    
                    # Parse directly to float16 to save memory
                    embedding = np.fromstring(embedding_str, sep=',', dtype=np.float32)
                    embedding = embedding.astype(np.float16)  # Convert to save memory
                    embeddings.append(embedding)
                    
                except (ValueError, AttributeError) as e:
                    logger.warning(f"Failed to parse embedding for ID {row.Id}: {e}")
                    ids.pop()
                    texts.pop()
                    continue
                
                processed += 1
        
        if not embeddings:
            raise RuntimeError("No valid embeddings found in database")
        
        # Convert to numpy arrays more efficiently
        logger.info("Converting embeddings to numpy array...")
        before_mem = get_memory_usage()
        
        embeddings_array = np.vstack(embeddings).astype(np.float16)
        
        # Clear intermediate list to free memory
        del embeddings
        force_garbage_collection()
        
        after_mem = get_memory_usage()
        logger.info(f"Embeddings array created. Memory: {before_mem:.1f}MB -> {after_mem:.1f}MB")
        logger.info(f"Loaded {len(ids)} records with {embeddings_array.shape[1]} dimensional embeddings (float16)")
        logger.info(f"Embeddings memory usage: {embeddings_array.nbytes / (1024*1024):.1f} MB")
        
        # Initialize BM25 for lexical search
        logger.info("Initializing BM25 for lexical search...")
        before_bm25 = get_memory_usage()
        bm25 = BM25(texts)
        after_bm25 = get_memory_usage()
        logger.info(f"BM25 initialized. Memory: {before_bm25:.1f}MB -> {after_bm25:.1f}MB")
        
        # Final cleanup
        force_garbage_collection()
        final_mem = get_memory_usage()
        logger.info(f"Cache loading complete. Final memory: {final_mem:.1f}MB")
        
        return {
            'ids': np.array(ids),
            'texts': texts,
            'embeddings': embeddings_array,  # Shape: (n_records, embedding_dim) - float16 for memory efficiency
            'bm25': bm25,
            'last_updated': time.time()
        }
        
    except Exception as e:
        logger.error(f"Failed to load search cache: {str(e)}")
        # Clean up on error
        force_garbage_collection()
        raise RuntimeError(f"Cache loading failed: {str(e)}")

# Keep original function name for compatibility
def load_search_cache():
    """Load search data using new chunked approach"""
    return load_search_cache_chunked()

def calculate_lexical_scores_bm25(bm25: DiskBackedBM25, query: str) -> np.ndarray:
    """Calculate BM25 lexical similarity scores for all texts, normalized to 0-1 range"""
    raw_scores = bm25.get_scores(query)

    # Normalize to 0-1 range to match semantic score scale
    if len(raw_scores) > 0:
        max_score = np.max(raw_scores)
        if max_score > 0:
            normalized_scores = raw_scores / max_score
        else:
            normalized_scores = raw_scores
    else:
        normalized_scores = raw_scores

    return normalized_scores

def fetch_full_records_by_ids(target_ids: List[str], scores: List[float]) -> List[Dict]:
    """Fetch complete records using ID list"""
    if not target_ids:
        return []
    
    try:
        # Create parameterized query for multiple IDs
        placeholders = ','.join([f':id_{i}' for i in range(len(target_ids))])
        params = {f'id_{i}': target_ids[i] for i in range(len(target_ids))}
        
        with engine.connect() as conn:
            result = conn.execute(text(f"""
                SELECT * FROM ReportNLP 
                WHERE Id IN ({placeholders})
            """), params)
            
            # Create lookup for scores by ID
            score_lookup = {target_ids[i]: scores[i] for i in range(len(target_ids))}
            
            records = []
            columns = result.keys()
            for row in result:
                record_dict = dict(zip(columns, row))
                record_dict['hybrid_score'] = score_lookup.get(str(row.Id), 0.0)
                records.append(record_dict)
            
            # Sort by score (since SQL IN doesn't preserve order)
            records.sort(key=lambda x: x['hybrid_score'], reverse=True)
            
        return records
        
    except Exception as e:
        logger.error(f"Failed to fetch records by IDs: {str(e)}")
        raise RuntimeError(f"Record retrieval failed: {str(e)}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events"""
    global model, engine, search_cache
    
    # Startup
    logger.info("Starting up Search API...")
    try:
        # Load embedding model
        logger.info("Loading SentenceTransformer model...")
        model = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info("Model loaded successfully")
        
        # Initialize database connection
        logger.info("Initializing database connection...")

        # Login to DB
        engine = create_engine(
            connection_url2,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False
        )
        
        # Test database connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("Database connection established")
        
        # Load search cache with chunked processing
        logger.info("Loading search cache with chunked processing...")
        search_cache = load_search_cache_chunked()
        logger.info(f"Search cache loaded: {len(search_cache['ids'])} records")
        logger.info(f"Memory-mapped storage: {search_cache.get('is_memory_mapped', False)}")
        
    except Exception as e:
        logger.error(f"Startup failed: {str(e)}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Search API...")
    if engine:
        engine.dispose()
        logger.info("Database connection closed")

# Create FastAPI app
app = FastAPI(
    title="Hybrid Search API",
    description="API for semantic and lexical search using embeddings",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class SearchRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    table_name: Optional[str] = Field(query_table, description="Target table name")
    text_column: Optional[str] = Field("extracted_text", description="Text column to search")
    embedding_column: Optional[str] = Field("embedding_vector", description="Embedding column name")
    top_k: Optional[int] = Field(10, ge=1, le=100, description="Number of results to return")
    semantic_weight: Optional[float] = Field(0.6, ge=0.0, le=1.0, description="Weight for semantic search")
    lexical_weight: Optional[float] = Field(0.4, ge=0.0, le=1.0, description="Weight for lexical search")
    min_similarity: Optional[float] = Field(0.1, ge=0.0, le=1.0, description="Minimum similarity threshold")
    include_scores: Optional[bool] = Field(True, description="Include similarity scores in response")
    
    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError('Query cannot be empty or whitespace only')
        return v.strip()
    
    @model_validator(mode='after')
    def validate_weights(self):
        if self.semantic_weight + self.lexical_weight == 0:
            raise ValueError('At least one weight must be greater than 0')
        return self

class SearchResult(BaseModel):
    Id: str
    Type: Optional[str]
    Niche_Report_ID: str
    Entered_Time: Optional[str]
    Report_Time: Optional[str]
    Remarks: Optional[str]
    Niche_Author_ID: Optional[str]
    Niche_Enter_ID: Optional[str]
    Niche_Occurrence_ID: Optional[str]
    Occurrence_Number: Optional[str]
    Occurrence_Type: Optional[str]
    Zone: Optional[str]
    Team: Optional[str]
    Municipality: Optional[str]
    AccessControlList: Optional[str]
    fixed_type: Optional[str]
    real_type: Optional[str]
    category: Optional[str]
    gzip: Optional[int]
    empty_report: Optional[int]
    empty_embedding: Optional[int]
    ETL_Proc_Time: Optional[str]
    extracted_text: Optional[str]
    semantic_score: Optional[float] = None
    lexical_score: Optional[float] = None
    hybrid_score: Optional[float] = None
    text_length: Optional[int] = None

class SearchResponse(BaseModel):
    success: bool
    query: str
    total_results: int
    execution_time_ms: float
    search_parameters: Dict[str, Any]
    results: List[SearchResult]

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_type: str
    timestamp: str
    request_id: Optional[str] = None

# Middleware for request logging and memory tracking
@app.middleware("http")
async def memory_tracking_middleware(request: Request, call_next):
    """Track memory usage per request"""
    start_time = time.time()
    start_memory = get_memory_usage()
    request_id = f"{int(time.time())}-{id(request)}"
    
    # Log request
    logger.info(f"Request {request_id}: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        
        end_memory = get_memory_usage()
        process_time = (time.time() - start_time) * 1000
        memory_diff = end_memory - start_memory
        
        # Log if memory increased significantly
        if memory_diff > 5:  # More than 5MB increase
            logger.warning(f"Request {request_id} increased memory by {memory_diff:.1f}MB "
                         f"(from {start_memory:.1f}MB to {end_memory:.1f}MB)")
        
        # Force GC every 50 requests or if memory increased too much
        if not hasattr(request.state, 'request_count'):
            request.state.request_count = 0
        request.state.request_count += 1
            
        if request.state.request_count % 50 == 0 or memory_diff > 10:
            freed = force_garbage_collection()
            if freed > 1:
                logger.info(f"Garbage collection freed {freed:.1f}MB after request {request_id}")
        
        logger.info(f"Request {request_id} completed in {process_time:.2f}ms with status {response.status_code}")
        return response
        
    except Exception as e:
        # Always run GC on exceptions
        force_garbage_collection()
        process_time = (time.time() - start_time) * 1000
        logger.error(f"Request {request_id} failed after {process_time:.2f}ms: {str(e)}")
        raise

# Custom exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            error_type="HTTPException",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            request_id=str(id(request))
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    error_id = str(id(request))
    logger.error(f"Unexpected error {error_id}: {str(exc)}\n{traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error. Please check logs for details.",
            error_type="InternalServerError",
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            request_id=error_id
        ).dict()
    )

# Helper functions
def validate_table_column_names(table_name: str, text_column: str, embedding_column: str):
    """Validate table and column names to prevent SQL injection"""
    import re
    
    # Very permissive pattern that allows most SQL Server naming conventions
    # but blocks obvious SQL injection attempts
    def is_valid_sql_identifier(name: str) -> bool:
        # Allow:
        # - Regular identifiers: table, [table], schema.table, [schema].[table]
        # - Temp tables: #table, ##table
        # - 4-part names: server.database.schema.table
        # - Quoted identifiers with spaces: [table name]
        
        # Block obvious injection attempts
        dangerous_patterns = [
            r';\s*drop\s+',
            r';\s*delete\s+',
            r';\s*insert\s+',
            r';\s*update\s+',
            r';\s*exec\s*\(',
            r';\s*execute\s*\(',
            r'--',
            r'/\*',
            r'\*/',
            r'union\s+select',
            r'or\s+1\s*=\s*1',
            r'and\s+1\s*=\s*1'
        ]
        
        name_lower = name.lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, name_lower):
                return False
        
        # Allow most valid SQL Server identifiers
        # This pattern is very permissive but safe
        valid_pattern = r'^[a-zA-Z0-9_#\[\].\s-]+$'
        return re.match(valid_pattern, name) is not None
    
    if not is_valid_sql_identifier(table_name):
        raise ValueError(f"Invalid or potentially unsafe table name: {table_name}")
    if not is_valid_sql_identifier(text_column):
        raise ValueError(f"Invalid or potentially unsafe text column name: {text_column}")
    if not is_valid_sql_identifier(embedding_column):
        raise ValueError(f"Invalid or potentially unsafe embedding column name: {embedding_column}")

def generate_query_embedding(query: str) -> str:
    """Generate embedding for search query"""
    try:
        if model is None:
            raise RuntimeError("Embedding model not loaded")
        
        logger.debug(f"Generating embedding for query: {query[:50]}...")
        embedding = model.encode([query], convert_to_tensor=False, show_progress_bar=False)[0]
        embedding_str = ','.join(map(str, embedding))
        logger.debug(f"Generated embedding with {len(embedding)} dimensions")
        return embedding_str
    except Exception as e:
        logger.error(f"Failed to generate embedding: {str(e)}")
        raise RuntimeError(f"Embedding generation failed: {str(e)}")

def execute_search_query(params: dict) -> List[Dict]:
    """Execute the hybrid search SQL query"""
    try:
        if engine is None:
            raise RuntimeError("Database connection not available")
        
        with engine.connect() as conn:
            logger.debug(f"Executing search on table: {params['table_name']}")
            
            result = conn.execute(
                text("""
                    EXEC HybridSearchDynamic
                        @table_name = :table_name,
                        @text_column = :text_column,
                        @embedding_column = :embedding_column,
                        @query = :query,
                        @query_embedding = :query_embedding,
                        @top_k = :top_k,
                        @semantic_weight = :semantic_weight,
                        @lexical_weight = :lexical_weight,
                        @min_similarity = :min_similarity
                """),
                params
            )
            
            # Convert to list of dictionaries
            columns = result.keys()
            rows = result.fetchall()
            results = [dict(zip(columns, row)) for row in rows]
            
            logger.debug(f"Query returned {len(results)} results")
            return results
            
    except exc.SQLAlchemyError as e:
        logger.error(f"Database error: {str(e)}")
        raise RuntimeError(f"Database query failed: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in query execution: {str(e)}")
        raise

async def optimized_hybrid_search(request: SearchRequest) -> SearchResponse:
    """Memory-optimized hybrid search using cached data + ID lookup"""
    start_time = time.time()
    start_memory = get_memory_usage()
    
    try:
        # Check if cache is available
        if search_cache is None:
            raise RuntimeError("Search cache not loaded")
        
        logger.info(f"Processing search: '{request.query}' (Memory: {start_memory:.1f}MB)")
        
        # Step 1: Generate query embedding
        query_embedding = model.encode([request.query], convert_to_tensor=False, show_progress_bar=False)[0]
        query_embedding = query_embedding.astype(np.float32)
        
        # Step 2: Calculate semantic similarities (batched for memory-mapped arrays)
        embeddings = search_cache['embeddings']
        total_docs = len(embeddings)
        semantic_scores = np.zeros(total_docs, dtype=np.float32)

        # Process in batches to control memory usage with memory-mapped arrays
        batch_size = 100000
        for i in range(0, total_docs, batch_size):
            end_idx = min(i + batch_size, total_docs)

            # Load batch from memory-mapped array and convert to float32
            batch_embeddings = embeddings[i:end_idx].astype(np.float32)

            # Calculate cosine similarity for this batch
            batch_scores = np.dot(batch_embeddings, query_embedding) / (
                np.linalg.norm(batch_embeddings, axis=1) * np.linalg.norm(query_embedding)
            )
            semantic_scores[i:end_idx] = batch_scores

            # Clean up batch
            del batch_embeddings
        
        # Step 3: Calculate lexical scores using BM25
        lexical_scores = calculate_lexical_scores_bm25(search_cache['bm25'], request.query)
        
        # Step 4: Combine scores (reuse arrays)
        hybrid_scores = (
            request.semantic_weight * semantic_scores + 
            request.lexical_weight * lexical_scores
        )
        
        # Step 5: Filter by minimum similarity and get top K
        valid_indices = np.where(hybrid_scores >= request.min_similarity)[0]
        
        if len(valid_indices) == 0:
            # Clean up before returning
            del semantic_scores, lexical_scores, hybrid_scores
            force_garbage_collection()
            
            logger.info("No results met minimum similarity threshold")
            return SearchResponse(
                success=True,
                query=request.query,
                total_results=0,
                execution_time_ms=(time.time() - start_time) * 1000,
                results=[],
                search_parameters={
                    'table_name': request.table_name,
                    'semantic_weight': request.semantic_weight,
                    'lexical_weight': request.lexical_weight,
                    'top_k': request.top_k,
                    'min_similarity': request.min_similarity
                }
            )
        
        # Get top K from valid results
        valid_scores = hybrid_scores[valid_indices]
        top_k_indices_in_valid = np.argsort(valid_scores)[-request.top_k:][::-1]
        top_indices = valid_indices[top_k_indices_in_valid]
        
        # Get target IDs and scores
        target_ids = search_cache['ids'][top_indices].tolist()
        target_scores = hybrid_scores[top_indices].tolist()
        # Store individual scores for response
        target_semantic_scores = semantic_scores[top_indices].tolist()
        target_lexical_scores = lexical_scores[top_indices].tolist()
        
        # Clean up large arrays
        del semantic_scores, lexical_scores, hybrid_scores, valid_indices, valid_scores
        
        search_time = time.time() - start_time
        
        # Step 6: Fetch full records from database
        full_records_data = fetch_full_records_by_ids(target_ids, target_scores)
        
        # Create lookup dictionaries for individual scores
        semantic_lookup = {target_ids[i]: target_semantic_scores[i] for i in range(len(target_ids))}
        lexical_lookup = {target_ids[i]: target_lexical_scores[i] for i in range(len(target_ids))}
        
        # Process results into SearchResult objects
        processed_results = []
        for row in full_records_data:
            record_id = str(row.get('Id', ''))
            result = SearchResult(
                Id=record_id,
                Type=row.get('Type'),
                Niche_Report_ID=str(row.get('Niche_Report_ID', '')),
                Entered_Time=str(row.get('Entered_Time', '')) if row.get('Entered_Time') else None,
                Report_Time=str(row.get('Report_Time', '')) if row.get('Report_Time') else None,
                Remarks=row.get('Remarks'),
                Niche_Author_ID=row.get('Niche_Author_ID'),
                Niche_Enter_ID=row.get('Niche_Enter_ID'),
                Niche_Occurrence_ID=row.get('Niche_Occurrence_ID'),
                Occurrence_Number=row.get('Occurrence_Number'),
                Occurrence_Type=row.get('Occurrence_Type'),
                Zone=row.get('Zone'),
                Team=row.get('Team'),
                Municipality=row.get('Municipality'),
                AccessControlList=row.get('AccessControlList'),
                fixed_type=row.get('fixed_type'),
                real_type=row.get('real_type'),
                category=row.get('category'),
                gzip=row.get('gzip'),
                empty_report=row.get('empty_report'),
                empty_embedding=row.get('empty_embedding'),
                ETL_Proc_Time=row.get('ETL_Proc_Time'),
                extracted_text=row.get('extracted_text'),
                semantic_score=float(semantic_lookup.get(record_id, 0)) if request.include_scores else None,
                lexical_score=float(lexical_lookup.get(record_id, 0)) if request.include_scores else None,
                hybrid_score=float(row.get('hybrid_score', 0)) if request.include_scores else None,
                text_length=len(row.get('extracted_text', '')) if request.include_scores else None
            )
            processed_results.append(result)
        
        total_time = (time.time() - start_time) * 1000
        end_memory = get_memory_usage()
        memory_used = end_memory - start_memory
        
        logger.info(f"Search completed: {len(processed_results)} results in {total_time:.2f}ms "
                   f"(Memory: {start_memory:.1f}MB -> {end_memory:.1f}MB, diff: {memory_used:+.1f}MB)")
        
        # Force cleanup if we used too much memory
        if memory_used > 5:
            force_garbage_collection()
        
        return SearchResponse(
            success=True,
            query=request.query,
            total_results=len(processed_results),
            execution_time_ms=total_time,
            results=processed_results,
            search_parameters={
                'table_name': request.table_name,
                'semantic_weight': request.semantic_weight,
                'lexical_weight': request.lexical_weight,
                'top_k': request.top_k,
                'min_similarity': request.min_similarity
            }
        )
        
    except Exception as e:
        # Always clean up on error
        force_garbage_collection()
        logger.error(f"Optimized search failed: {str(e)}")
        raise RuntimeError(f"Search execution failed: {str(e)}")

@app.get("/memory")
async def memory_status():
    """Get current memory usage statistics"""
    try:
        current_mem = get_memory_usage()
        cache_size = len(search_cache['ids']) if search_cache else 0
        
        # Get process info
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            "memory_mb": round(current_mem, 1),
            "memory_gb": round(current_mem / 1024, 2),
            "cache_records": cache_size,
            "memory_per_record_kb": round((current_mem * 1024) / cache_size, 2) if cache_size > 0 else 0,
            "virtual_memory_mb": round(memory_info.vms / 1024 / 1024, 1),
            "peak_memory_mb": round(memory_info.peak_wset / 1024 / 1024, 1) if hasattr(memory_info, 'peak_wset') else None,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "process_id": os.getpid()
        }
    except Exception as e:
        logger.error(f"Memory check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Memory check failed")

@app.post("/gc")
async def manual_garbage_collection():
    """Manually trigger garbage collection"""
    try:
        before = get_memory_usage()
        freed = force_garbage_collection()
        after = get_memory_usage()
        
        return {
            "success": True,
            "memory_before_mb": round(before, 1),
            "memory_after_mb": round(after, 1),
            "memory_freed_mb": round(freed, 1),
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
    except Exception as e:
        logger.error(f"Manual GC failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Garbage collection failed")

# API Endpoints
@app.post("/search", response_model=SearchResponse)
async def hybrid_search(request: SearchRequest):
    """
    Perform hybrid semantic and lexical search
    """
    try:
        logger.info(f"Processing search request: '{request.query}' on table '{request.table_name}'")
        
        # Use optimized search instead of stored procedure
        return await optimized_hybrid_search(request)
        
    except ValueError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except RuntimeError as e:
        logger.error(f"Runtime error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="An unexpected error occurred")

@app.get("/search")
async def simple_search(
        q: str,
        table: str = query_table,
        text_column: str = "extracted_text",
        embedding_column: str = "embedding_vector",
        top_k: int = 5,
        semantic_weight: float = 0.8,
        lexical_weight: float = 0.2,
        min_similarity: float = 0.1,
        include_scores: bool = True
    ):
    """
    Simple GET endpoint for basic searches with all parameters
    """
    search_request = SearchRequest(
        query=q,
        table_name=table,
        text_column=text_column,
        embedding_column=embedding_column,
        top_k=top_k,
        semantic_weight=semantic_weight,
        lexical_weight=lexical_weight,
        min_similarity=min_similarity,
        include_scores=include_scores
    )
    return await hybrid_search(search_request)

@app.get("/health")
async def health_check():
    """
    Health check endpoint
    """
    try:
        # Check model
        model_status = model is not None
        
        # Check database
        db_status = False
        if engine:
            try:
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                db_status = True
            except Exception:
                pass
        
        # Check cache
        cache_status = search_cache is not None
        cache_size = len(search_cache['ids']) if cache_status else 0
        cache_age = time.time() - search_cache['last_updated'] if cache_status else None
        
        status = "healthy" if (model_status and db_status and cache_status) else "unhealthy"
        
        return {
            "status": status,
            "model_loaded": model_status,
            "database_connected": db_status,
            "cache_loaded": cache_status,
            "cache_size": cache_size,
            "cache_age_seconds": cache_age,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "data_access": "ConnectorX + SQLAlchemy (hybrid)"
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Health check failed")

@app.post("/refresh-cache")
async def refresh_cache():
    """
    Manually refresh the search cache
    """
    global search_cache
    
    try:
        logger.info("Manual cache refresh requested")
        old_size = len(search_cache['ids']) if search_cache else 0
        
        search_cache = load_search_cache_chunked()
        new_size = len(search_cache['ids'])
        
        logger.info(f"Cache refreshed: {old_size} -> {new_size} records")
        
        return {
            "success": True,
            "message": f"Cache refreshed successfully",
            "old_size": old_size,
            "new_size": new_size,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
    except Exception as e:
        logger.error(f"Cache refresh failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Cache refresh failed: {str(e)}")

@app.get("/")
async def root():
    """
    Root endpoint with API information
    """
    return {
        "message": "Hybrid Search API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "endpoints": {
            "POST /search": "Hybrid search via POST request with JSON body",
            "GET /search": "Hybrid search via GET request",
            "POST /refresh-cache": "Manually refresh the search cache",
            "GET /memory": "Check current memory usage statistics",
            "POST /gc": "Manually trigger garbage collection",
        },
        "performance": {
            "approach": "Chunked loading + memory-mapped embeddings + disk-backed BM25",
            "memory_optimization": "Memory-mapped float16 embeddings, disk-backed BM25, chunked processing",
            "data_loading": "ConnectorX chunked loading with minimal RAM usage",
            "scalability": "Supports datasets much larger than available RAM"
        }
    }

# Run the application
if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True
    )