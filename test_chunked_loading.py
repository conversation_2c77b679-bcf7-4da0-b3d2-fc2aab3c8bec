#!/usr/bin/env python3
"""
Test script for the new chunked loading approach
This script tests the memory-mapped embeddings and disk-backed BM25 implementation
"""

import os
import sys
import time
import psutil
import numpy as np

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_chunked_loading():
    """Test the new chunked loading approach"""
    print("Testing Chunked Loading Approach")
    print("=" * 50)
    
    # Import after setting up path
    try:
        from backend_api import (
            load_search_cache_chunked, 
            DiskBackedBM25,
            get_text_chunks_generator,
            load_embeddings_chunked,
            engine,
            connection_url2
        )
        from sqlalchemy import create_engine
        
        print("✓ Successfully imported chunked loading functions")
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    
    # Initialize database connection for testing
    try:
        print("\n1. Testing Database Connection...")
        test_engine = create_engine(
            connection_url2,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False
        )
        
        with test_engine.connect() as conn:
            from sqlalchemy import text
            result = conn.execute(text("SELECT COUNT(*) FROM ReportNLP WHERE empty_report = 0 AND empty_embedding = 0"))
            total_count = result.fetchone()[0]
            print(f"✓ Database connected. Found {total_count} valid records")
            
        # Set global engine for the functions
        import backend_api
        backend_api.engine = test_engine
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False
    
    # Test memory usage before loading
    initial_memory = get_memory_usage()
    print(f"\n2. Initial Memory Usage: {initial_memory:.1f} MB")
    
    # Test chunked loading
    try:
        print("\n3. Testing Chunked Cache Loading...")
        start_time = time.time()
        
        cache = load_search_cache_chunked()
        
        load_time = time.time() - start_time
        final_memory = get_memory_usage()
        memory_increase = final_memory - initial_memory
        
        print(f"✓ Cache loaded successfully in {load_time:.2f} seconds")
        print(f"✓ Records loaded: {len(cache['ids'])}")
        print(f"✓ Memory usage: {initial_memory:.1f} MB → {final_memory:.1f} MB (+{memory_increase:.1f} MB)")
        print(f"✓ Memory-mapped: {cache.get('is_memory_mapped', False)}")
        print(f"✓ Cache directory: {cache.get('cache_dir', 'N/A')}")
        
        # Test embeddings access
        print(f"✓ Embeddings shape: {cache['embeddings'].shape}")
        print(f"✓ Embeddings dtype: {cache['embeddings'].dtype}")
        
        # Test BM25 access
        print(f"✓ BM25 type: {type(cache['bm25'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Chunked loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_performance():
    """Test search performance with the new approach"""
    print("\n" + "=" * 50)
    print("Testing Search Performance")
    print("=" * 50)
    
    try:
        from backend_api import optimized_hybrid_search, SearchRequest
        
        # Create a test search request
        test_request = SearchRequest(
            query="safety incident report",
            top_k=10,
            semantic_weight=0.6,
            lexical_weight=0.4,
            min_similarity=0.1
        )
        
        print(f"Test query: '{test_request.query}'")
        
        # Measure search performance
        start_time = time.time()
        start_memory = get_memory_usage()
        
        # Note: This would require the full FastAPI context to work
        # For now, just test that the function exists
        print("✓ Search function is available")
        print("✓ Search request model is valid")
        
        return True
        
    except Exception as e:
        print(f"✗ Search test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Chunked Loading Test Suite")
    print("=" * 50)
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Process PID: {os.getpid()}")
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_chunked_loading():
        tests_passed += 1
    
    if test_search_performance():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! The chunked loading approach is working.")
        return True
    else:
        print("✗ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
