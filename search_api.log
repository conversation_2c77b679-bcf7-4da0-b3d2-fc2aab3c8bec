2025-07-15 14:53:34,972 - __main__ - INFO - Starting up Search API...
2025-07-15 14:53:34,973 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-15 14:53:34,977 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-15 14:53:34,978 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-15 14:53:39,374 - __main__ - INFO - Model loaded successfully
2025-07-15 14:53:39,374 - __main__ - INFO - Initializing database connection...
2025-07-15 14:53:39,605 - __main__ - INFO - Database connection established
2025-07-15 14:53:39,607 - __main__ - INFO - Loading search cache...
2025-07-15 14:53:39,607 - __main__ - INFO - Loading search data into memory...
2025-07-15 14:53:39,996 - __main__ - INFO - Processing records...
2025-07-15 14:53:39,996 - __main__ - INFO - Processed 0 records...
2025-07-15 14:53:43,148 - __main__ - INFO - Processed 10000 records...
2025-07-15 14:53:46,553 - __main__ - INFO - Processed 20000 records...
2025-07-15 14:53:50,073 - __main__ - INFO - Processed 30000 records...
2025-07-15 14:53:54,068 - __main__ - INFO - Processed 40000 records...
2025-07-15 14:53:57,715 - __main__ - INFO - Processed 50000 records...
2025-07-15 14:54:01,228 - __main__ - INFO - Processed 60000 records...
2025-07-15 14:54:04,604 - __main__ - INFO - Processed 70000 records...
2025-07-15 14:54:08,181 - __main__ - INFO - Processed 80000 records...
2025-07-15 14:54:11,964 - __main__ - INFO - Processed 90000 records...
2025-07-15 14:54:13,352 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings
2025-07-15 14:54:13,406 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-15 14:54:26,317 - __main__ - INFO - Request 1752605666-2291471700128: GET http://localhost:8000/search?q=narcotics&top_k=3&semantic_weight=0.5&lexical_weight=0.5&min_similarity=0.1&table=ReportNLP
2025-07-15 14:54:26,319 - __main__ - INFO - Processing search request: 'narcotics' on table 'ReportNLP'
2025-07-15 14:54:26,319 - __main__ - INFO - Processing optimized search: 'narcotics' on 94502 cached records
2025-07-15 14:54:35,343 - __main__ - INFO - Optimized search completed: 3 results in 9023.32ms (search: 8967.54ms, DB fetch: 55.78ms)
2025-07-15 14:54:35,345 - __main__ - INFO - Request 1752605666-2291471700128 completed in 9027.31ms with status 200
2025-07-15 14:56:02,900 - __main__ - INFO - Shutting down Search API...
2025-07-15 14:56:02,903 - __main__ - INFO - Database connection closed
2025-07-15 15:02:52,940 - __main__ - INFO - Starting up Search API...
2025-07-15 15:02:52,941 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-15 15:02:52,947 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-15 15:02:52,947 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-15 15:02:56,298 - __main__ - INFO - Model loaded successfully
2025-07-15 15:02:56,299 - __main__ - INFO - Initializing database connection...
2025-07-15 15:02:56,465 - __main__ - INFO - Database connection established
2025-07-15 15:02:56,465 - __main__ - INFO - Loading search cache...
2025-07-15 15:02:56,465 - __main__ - INFO - Loading search data into memory...
2025-07-15 15:02:56,469 - __main__ - INFO - Processing records...
2025-07-15 15:02:56,470 - __main__ - INFO - Processed 0 records...
2025-07-15 15:02:59,481 - __main__ - INFO - Processed 10000 records...
2025-07-15 15:03:02,675 - __main__ - INFO - Processed 20000 records...
2025-07-15 15:03:06,041 - __main__ - INFO - Processed 30000 records...
2025-07-15 15:03:09,399 - __main__ - INFO - Processed 40000 records...
2025-07-15 15:03:12,789 - __main__ - INFO - Processed 50000 records...
2025-07-15 15:03:16,136 - __main__ - INFO - Processed 60000 records...
2025-07-15 15:03:19,609 - __main__ - INFO - Processed 70000 records...
2025-07-15 15:03:22,959 - __main__ - INFO - Processed 80000 records...
2025-07-15 15:03:26,376 - __main__ - INFO - Processed 90000 records...
2025-07-15 15:03:27,792 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings
2025-07-15 15:03:27,830 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-15 15:03:40,470 - __main__ - INFO - Request 1752606220-2232995109184: GET http://localhost:8000/search?q=theft&top_k=3&semantic_weight=0.5&lexical_weight=0.5&min_similarity=0.1&table=ReportNLP
2025-07-15 15:03:40,471 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-15 15:03:40,472 - __main__ - INFO - Processing optimized search: 'theft' on 94502 cached records
2025-07-15 15:03:49,427 - __main__ - INFO - Optimized search completed: 3 results in 8955.49ms (search: 8883.83ms, DB fetch: 71.66ms)
2025-07-15 15:03:49,429 - __main__ - INFO - Request 1752606220-2232995109184 completed in 8959.50ms with status 200
2025-07-15 15:04:33,164 - __main__ - INFO - Shutting down Search API...
2025-07-15 15:04:33,167 - __main__ - INFO - Database connection closed
2025-07-15 15:13:29,858 - __main__ - INFO - Starting up Search API...
2025-07-15 15:13:29,859 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-15 15:13:29,862 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-15 15:13:29,863 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-15 15:13:33,601 - __main__ - INFO - Model loaded successfully
2025-07-15 15:13:33,601 - __main__ - INFO - Initializing database connection...
2025-07-15 15:13:33,737 - __main__ - INFO - Database connection established
2025-07-15 15:13:33,737 - __main__ - INFO - Loading search cache...
2025-07-15 15:13:33,738 - __main__ - INFO - Loading search data into memory...
2025-07-15 15:13:33,752 - __main__ - INFO - Processing records...
2025-07-15 15:13:33,753 - __main__ - INFO - Processed 0 records...
2025-07-15 15:13:36,835 - __main__ - INFO - Processed 10000 records...
2025-07-15 15:13:39,642 - __main__ - INFO - Processed 20000 records...
2025-07-15 15:13:42,975 - __main__ - INFO - Processed 30000 records...
2025-07-15 15:13:46,468 - __main__ - INFO - Processed 40000 records...
2025-07-15 15:13:49,644 - __main__ - INFO - Processed 50000 records...
2025-07-15 15:13:53,404 - __main__ - INFO - Processed 60000 records...
2025-07-15 15:13:56,566 - __main__ - INFO - Processed 70000 records...
2025-07-15 15:13:59,794 - __main__ - INFO - Processed 80000 records...
2025-07-15 15:14:03,181 - __main__ - INFO - Processed 90000 records...
2025-07-15 15:14:05,339 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings
2025-07-15 15:14:05,340 - __main__ - INFO - Initializing BM25 for lexical search...
2025-07-15 15:14:05,340 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-15 15:14:09,338 - __main__ - INFO - Processed 50000/94502 documents for BM25
2025-07-15 15:14:17,154 - __main__ - INFO - Building term document frequencies...
2025-07-15 15:14:20,298 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-15 15:14:20,298 - __main__ - INFO - BM25 initialized successfully
2025-07-15 15:14:20,327 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-15 15:14:31,431 - __main__ - INFO - Request 1752606871-1952685322080: GET http://localhost:8000/search?q=theft&top_k=5&semantic_weight=0.5&lexical_weight=0.5&min_similarity=0.1&table=ReportNLP
2025-07-15 15:14:31,432 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-15 15:14:31,433 - __main__ - INFO - Processing optimized search with BM25: 'theft' on 94502 cached records
2025-07-15 15:14:31,739 - __main__ - INFO - Optimized search completed: 5 results in 305.34ms (search: 213.61ms, DB fetch: 91.73ms)
2025-07-15 15:14:31,741 - __main__ - INFO - Request 1752606871-1952685322080 completed in 309.34ms with status 200
2025-07-15 15:15:00,787 - __main__ - INFO - Shutting down Search API...
2025-07-15 15:15:00,790 - __main__ - INFO - Database connection closed
2025-07-15 15:17:50,236 - __main__ - INFO - Starting up Search API...
2025-07-15 15:17:50,236 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-15 15:17:50,241 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-15 15:17:50,241 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-15 15:17:53,830 - __main__ - INFO - Model loaded successfully
2025-07-15 15:17:53,830 - __main__ - INFO - Initializing database connection...
2025-07-15 15:17:53,981 - __main__ - INFO - Database connection established
2025-07-15 15:17:53,981 - __main__ - INFO - Loading search cache...
2025-07-15 15:17:53,981 - __main__ - INFO - Loading search data into memory...
2025-07-15 15:17:53,986 - __main__ - INFO - Processing records...
2025-07-15 15:17:53,987 - __main__ - INFO - Processed 0 records...
2025-07-15 15:17:56,751 - __main__ - INFO - Processed 10000 records...
2025-07-15 15:17:59,772 - __main__ - INFO - Processed 20000 records...
2025-07-15 15:18:03,299 - __main__ - INFO - Processed 30000 records...
2025-07-15 15:18:06,390 - __main__ - INFO - Processed 40000 records...
2025-07-15 15:18:09,831 - __main__ - INFO - Processed 50000 records...
2025-07-15 15:18:13,190 - __main__ - INFO - Processed 60000 records...
2025-07-15 15:18:16,793 - __main__ - INFO - Processed 70000 records...
2025-07-15 15:18:20,428 - __main__ - INFO - Processed 80000 records...
2025-07-15 15:18:24,396 - __main__ - INFO - Processed 90000 records...
2025-07-15 15:18:26,739 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings
2025-07-15 15:18:26,740 - __main__ - INFO - Initializing BM25 for lexical search...
2025-07-15 15:18:26,740 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-15 15:18:32,187 - __main__ - INFO - Processed 50000/94502 documents for BM25
2025-07-15 15:18:40,559 - __main__ - INFO - Building term document frequencies...
2025-07-15 15:18:43,674 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-15 15:18:43,675 - __main__ - INFO - BM25 initialized successfully
2025-07-15 15:18:43,711 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-15 15:19:11,498 - __main__ - INFO - Request 1752607151-2062207448176: GET http://localhost:8000/search?q=car%20theft&top_k=5&semantic_weight=0.5&lexical_weight=0.5&min_similarity=0.1&table=ReportNLP
2025-07-15 15:19:11,500 - __main__ - INFO - Processing search request: 'car theft' on table 'ReportNLP'
2025-07-15 15:19:11,500 - __main__ - INFO - Processing optimized search with BM25: 'car theft' on 94502 cached records
2025-07-15 15:19:11,854 - __main__ - INFO - Optimized search completed: 5 results in 354.06ms (search: 278.82ms, DB fetch: 75.24ms)
2025-07-15 15:19:11,857 - __main__ - INFO - Request 1752607151-2062207448176 completed in 359.06ms with status 200
2025-07-15 15:19:59,409 - __main__ - INFO - Request 1752607199-2059911211888: GET http://localhost:8000/search?q=car%20theft&top_k=20&semantic_weight=0.5&lexical_weight=0.5&min_similarity=0.1&table=ReportNLP
2025-07-15 15:19:59,410 - __main__ - INFO - Processing search request: 'car theft' on table 'ReportNLP'
2025-07-15 15:19:59,411 - __main__ - INFO - Processing optimized search with BM25: 'car theft' on 94502 cached records
2025-07-15 15:19:59,935 - __main__ - INFO - Optimized search completed: 20 results in 524.44ms (search: 241.45ms, DB fetch: 282.99ms)
2025-07-15 15:19:59,939 - __main__ - INFO - Request 1752607199-2059911211888 completed in 528.44ms with status 200
2025-07-16 08:20:27,179 - __main__ - INFO - Request 1752668427-2059957851616: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.5&lexical_weight=0.5&min_similarity=0.1&table=ReportNLP
2025-07-16 08:20:27,180 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-16 08:20:27,180 - __main__ - INFO - Processing optimized search with BM25: 'theft' on 94502 cached records
2025-07-16 08:20:27,663 - __main__ - INFO - Optimized search completed: 20 results in 483.75ms (search: 228.39ms, DB fetch: 255.36ms)
2025-07-16 08:20:27,667 - __main__ - INFO - Request 1752668427-2059957851616 completed in 488.75ms with status 200
2025-07-16 08:21:33,314 - __main__ - INFO - Request 1752668493-2059957253808: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 08:21:33,315 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-16 08:21:33,315 - __main__ - INFO - Processing optimized search with BM25: 'theft' on 94502 cached records
2025-07-16 08:21:33,769 - __main__ - INFO - Optimized search completed: 20 results in 453.94ms (search: 200.44ms, DB fetch: 253.50ms)
2025-07-16 08:21:33,773 - __main__ - INFO - Request 1752668493-2059957253808 completed in 458.94ms with status 200
2025-07-16 12:18:47,530 - __main__ - INFO - Starting up Search API...
2025-07-16 12:18:47,531 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 12:18:47,535 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 12:18:47,536 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 12:18:50,600 - __main__ - INFO - Model loaded successfully
2025-07-16 12:18:50,601 - __main__ - INFO - Initializing database connection...
2025-07-16 12:18:50,758 - __main__ - INFO - Database connection established
2025-07-16 12:18:50,758 - __main__ - INFO - Loading search cache...
2025-07-16 12:18:50,759 - __main__ - INFO - Loading search data into memory...
2025-07-16 12:18:50,773 - __main__ - INFO - Processing records...
2025-07-16 12:18:50,773 - __main__ - INFO - Processed 0 records...
2025-07-16 12:18:52,857 - __main__ - INFO - Processed 10000 records...
2025-07-16 12:18:55,136 - __main__ - INFO - Processed 20000 records...
2025-07-16 12:18:57,709 - __main__ - INFO - Processed 30000 records...
2025-07-16 12:19:00,426 - __main__ - INFO - Processed 40000 records...
2025-07-16 12:19:02,646 - __main__ - INFO - Processed 50000 records...
2025-07-16 12:19:04,898 - __main__ - INFO - Processed 60000 records...
2025-07-16 12:19:07,922 - __main__ - INFO - Processed 70000 records...
2025-07-16 12:19:11,016 - __main__ - INFO - Processed 80000 records...
2025-07-16 12:19:14,547 - __main__ - INFO - Processed 90000 records...
2025-07-16 12:19:15,718 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings
2025-07-16 12:19:15,719 - __main__ - INFO - Initializing BM25 for lexical search...
2025-07-16 12:19:15,719 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 12:19:18,847 - __main__ - INFO - Processed 50000/94502 documents for BM25
2025-07-16 12:19:24,821 - __main__ - INFO - Building term document frequencies...
2025-07-16 12:19:27,990 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 12:19:27,991 - __main__ - INFO - BM25 initialized successfully
2025-07-16 12:19:28,027 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 12:20:27,687 - __main__ - INFO - Request 1752682827-1669217809712: GET http://localhost:8000/search?q=domestic%20violence&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 12:20:27,689 - __main__ - INFO - Processing search request: 'domestic violence' on table 'ReportNLP'
2025-07-16 12:20:27,689 - __main__ - INFO - Processing optimized search with BM25: 'domestic violence' on 94502 cached records
2025-07-16 12:20:28,133 - __main__ - INFO - Optimized search completed: 20 results in 444.34ms (search: 153.92ms, DB fetch: 290.42ms)
2025-07-16 12:20:28,135 - __main__ - INFO - Request 1752682827-1669217809712 completed in 448.34ms with status 200
2025-07-16 12:33:48,238 - __main__ - INFO - Shutting down Search API...
2025-07-16 12:33:48,240 - __main__ - INFO - Database connection closed
2025-07-16 12:39:03,930 - __main__ - INFO - Starting up Search API...
2025-07-16 12:39:03,931 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 12:39:03,934 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 12:39:03,935 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 12:39:06,421 - __main__ - INFO - Model loaded successfully
2025-07-16 12:39:06,421 - __main__ - INFO - Initializing database connection...
2025-07-16 12:39:06,556 - __main__ - INFO - Database connection established
2025-07-16 12:39:06,557 - __main__ - INFO - Loading search cache...
2025-07-16 12:39:06,557 - __main__ - INFO - Loading search data into memory...
2025-07-16 12:39:06,561 - __main__ - INFO - Processing records...
2025-07-16 12:39:06,562 - __main__ - INFO - Processed 0 records...
2025-07-16 12:39:08,760 - __main__ - INFO - Processed 10000 records...
2025-07-16 12:39:10,956 - __main__ - INFO - Processed 20000 records...
2025-07-16 12:39:14,086 - __main__ - INFO - Processed 30000 records...
2025-07-16 12:39:16,649 - __main__ - INFO - Processed 40000 records...
2025-07-16 12:39:18,862 - __main__ - INFO - Processed 50000 records...
2025-07-16 12:39:22,062 - __main__ - INFO - Processed 60000 records...
2025-07-16 12:39:24,934 - __main__ - INFO - Processed 70000 records...
2025-07-16 12:39:27,247 - __main__ - INFO - Processed 80000 records...
2025-07-16 12:39:29,644 - __main__ - INFO - Processed 90000 records...
2025-07-16 12:39:31,181 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings
2025-07-16 12:39:31,181 - __main__ - INFO - Initializing BM25 for lexical search...
2025-07-16 12:39:31,182 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 12:39:34,497 - __main__ - INFO - Processed 50000/94502 documents for BM25
2025-07-16 12:39:40,306 - __main__ - INFO - Building term document frequencies...
2025-07-16 12:39:42,773 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 12:39:42,773 - __main__ - INFO - BM25 initialized successfully
2025-07-16 12:39:42,802 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 12:53:42,029 - __main__ - INFO - Shutting down Search API...
2025-07-16 12:53:42,032 - __main__ - INFO - Database connection closed
2025-07-16 12:56:27,911 - __main__ - INFO - Starting up Search API...
2025-07-16 12:56:27,912 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 12:56:27,915 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 12:56:27,916 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 12:56:30,813 - __main__ - INFO - Model loaded successfully
2025-07-16 12:56:30,814 - __main__ - INFO - Initializing database connection...
2025-07-16 12:56:31,086 - __main__ - INFO - Database connection established
2025-07-16 12:56:31,086 - __main__ - INFO - Loading search cache...
2025-07-16 12:56:31,086 - __main__ - INFO - Loading search data into memory...
2025-07-16 12:56:31,090 - __main__ - INFO - Processing records...
2025-07-16 12:56:31,090 - __main__ - INFO - Processed 0 records...
2025-07-16 12:56:33,228 - __main__ - INFO - Processed 10000 records...
2025-07-16 12:56:35,991 - __main__ - INFO - Processed 20000 records...
2025-07-16 12:56:38,655 - __main__ - INFO - Processed 30000 records...
2025-07-16 12:56:41,469 - __main__ - INFO - Processed 40000 records...
2025-07-16 12:56:44,337 - __main__ - INFO - Processed 50000 records...
2025-07-16 12:56:47,026 - __main__ - INFO - Processed 60000 records...
2025-07-16 12:56:49,799 - __main__ - INFO - Processed 70000 records...
2025-07-16 12:56:53,365 - __main__ - INFO - Processed 80000 records...
2025-07-16 12:56:56,281 - __main__ - INFO - Processed 90000 records...
2025-07-16 12:56:57,797 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings (float16)
2025-07-16 12:56:57,798 - __main__ - INFO - Embeddings memory usage: 69.2 MB
2025-07-16 12:56:57,798 - __main__ - INFO - Initializing BM25 for lexical search...
2025-07-16 12:56:57,798 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 12:57:01,150 - __main__ - INFO - Processed 50000/94502 documents for BM25
2025-07-16 12:57:06,808 - __main__ - INFO - Building term document frequencies...
2025-07-16 12:57:09,452 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 12:57:09,453 - __main__ - INFO - BM25 initialized successfully
2025-07-16 12:57:09,482 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 12:58:07,161 - __main__ - INFO - Request 1752685087-2016974631232: GET http://localhost:8000/search?q=domestic%20violence&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 12:58:07,163 - __main__ - INFO - Processing search request: 'domestic violence' on table 'ReportNLP'
2025-07-16 12:58:07,164 - __main__ - INFO - Processing optimized search with BM25: 'domestic violence' on 94502 cached records
2025-07-16 12:58:07,708 - __main__ - INFO - Optimized search completed: 20 results in 543.98ms (search: 297.83ms, DB fetch: 246.15ms)
2025-07-16 12:58:07,723 - __main__ - INFO - Request 1752685087-2016974631232 completed in 561.97ms with status 200
2025-07-16 12:58:18,578 - __main__ - INFO - Request 1752685098-2016149638080: GET http://localhost:8000/search?q=vandalism&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 12:58:18,579 - __main__ - INFO - Processing search request: 'vandalism' on table 'ReportNLP'
2025-07-16 12:58:18,580 - __main__ - INFO - Processing optimized search with BM25: 'vandalism' on 94502 cached records
2025-07-16 12:58:19,057 - __main__ - INFO - Optimized search completed: 20 results in 477.11ms (search: 209.66ms, DB fetch: 267.45ms)
2025-07-16 12:58:19,074 - __main__ - INFO - Request 1752685098-2016149638080 completed in 496.11ms with status 200
2025-07-16 13:00:16,863 - __main__ - INFO - Request 1752685216-2016167591040: GET http://localhost:8000/search?q=steal%20car&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:00:16,863 - __main__ - INFO - Processing search request: 'steal car' on table 'ReportNLP'
2025-07-16 13:00:16,865 - __main__ - INFO - Processing optimized search with BM25: 'steal car' on 94502 cached records
2025-07-16 13:00:17,343 - __main__ - INFO - Optimized search completed: 20 results in 476.88ms (search: 204.63ms, DB fetch: 272.25ms)
2025-07-16 13:00:17,360 - __main__ - INFO - Request 1752685216-2016167591040 completed in 496.34ms with status 200
2025-07-16 13:01:50,520 - __main__ - INFO - Request 1752685310-2016167553664: GET http://localhost:8000/search?q=car%20crash&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:01:50,521 - __main__ - INFO - Processing search request: 'car crash' on table 'ReportNLP'
2025-07-16 13:01:50,522 - __main__ - INFO - Processing optimized search with BM25: 'car crash' on 94502 cached records
2025-07-16 13:01:51,026 - __main__ - INFO - Optimized search completed: 20 results in 504.12ms (search: 241.60ms, DB fetch: 262.52ms)
2025-07-16 13:01:51,042 - __main__ - INFO - Request 1752685310-2016167553664 completed in 522.33ms with status 200
2025-07-16 13:02:54,308 - __main__ - INFO - Request 1752685374-2016167557600: GET http://localhost:8000/search?q=nike&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:02:54,308 - __main__ - INFO - Processing search request: 'nike' on table 'ReportNLP'
2025-07-16 13:02:54,309 - __main__ - INFO - Processing optimized search with BM25: 'nike' on 94502 cached records
2025-07-16 13:02:54,775 - __main__ - INFO - Optimized search completed: 20 results in 465.70ms (search: 179.88ms, DB fetch: 285.82ms)
2025-07-16 13:02:54,790 - __main__ - INFO - Request 1752685374-2016167557600 completed in 481.70ms with status 200
2025-07-16 13:04:40,735 - __main__ - INFO - Request 1752685480-2016167554768: GET http://localhost:8000/search?q=fight&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:04:40,736 - __main__ - INFO - Processing search request: 'fight' on table 'ReportNLP'
2025-07-16 13:04:40,737 - __main__ - INFO - Processing optimized search with BM25: 'fight' on 94502 cached records
2025-07-16 13:04:41,189 - __main__ - INFO - Optimized search completed: 20 results in 451.67ms (search: 169.22ms, DB fetch: 282.45ms)
2025-07-16 13:04:41,205 - __main__ - INFO - Request 1752685480-2016167554768 completed in 469.66ms with status 200
2025-07-16 13:05:59,286 - __main__ - INFO - Request 1752685559-2016167543296: GET http://localhost:8000/search?q=bike&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:05:59,287 - __main__ - INFO - Processing search request: 'bike' on table 'ReportNLP'
2025-07-16 13:05:59,287 - __main__ - INFO - Processing optimized search with BM25: 'bike' on 94502 cached records
2025-07-16 13:05:59,730 - __main__ - INFO - Optimized search completed: 20 results in 443.22ms (search: 174.37ms, DB fetch: 268.85ms)
2025-07-16 13:05:59,746 - __main__ - INFO - Request 1752685559-2016167543296 completed in 460.24ms with status 200
2025-07-16 13:06:18,746 - __main__ - INFO - Request 1752685578-2016167547472: GET http://localhost:8000/search?q=bicycle&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:06:18,747 - __main__ - INFO - Processing search request: 'bicycle' on table 'ReportNLP'
2025-07-16 13:06:18,748 - __main__ - INFO - Processing optimized search with BM25: 'bicycle' on 94502 cached records
2025-07-16 13:06:19,220 - __main__ - INFO - Optimized search completed: 20 results in 472.28ms (search: 194.07ms, DB fetch: 278.22ms)
2025-07-16 13:06:19,235 - __main__ - INFO - Request 1752685578-2016167547472 completed in 489.28ms with status 200
2025-07-16 13:08:23,019 - __main__ - INFO - Shutting down Search API...
2025-07-16 13:08:23,023 - __main__ - INFO - Database connection closed
2025-07-16 13:14:56,851 - __main__ - INFO - Starting up Search API...
2025-07-16 13:14:56,851 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 13:14:56,855 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 13:14:56,855 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 13:14:59,387 - __main__ - INFO - Model loaded successfully
2025-07-16 13:14:59,387 - __main__ - INFO - Initializing database connection...
2025-07-16 13:14:59,504 - __main__ - INFO - Database connection established
2025-07-16 13:14:59,504 - __main__ - INFO - Loading search cache...
2025-07-16 13:14:59,504 - __main__ - INFO - Loading search data into memory...
2025-07-16 13:14:59,511 - __main__ - INFO - Processing records...
2025-07-16 13:14:59,511 - __main__ - INFO - Processed 0 records. Memory: 433.1MB
2025-07-16 13:15:00,627 - __main__ - INFO - Processed 5000 records. Memory: 439.0MB
2025-07-16 13:15:01,886 - __main__ - INFO - Processed 10000 records. Memory: 445.6MB
2025-07-16 13:15:03,167 - __main__ - INFO - Processed 15000 records. Memory: 454.1MB
2025-07-16 13:15:04,630 - __main__ - INFO - Processed 20000 records. Memory: 461.3MB
2025-07-16 13:15:06,042 - __main__ - INFO - Processed 25000 records. Memory: 470.2MB
2025-07-16 13:15:07,501 - __main__ - INFO - Processed 30000 records. Memory: 478.7MB
2025-07-16 13:15:08,871 - __main__ - INFO - Processed 35000 records. Memory: 489.1MB
2025-07-16 13:15:10,371 - __main__ - INFO - Processed 40000 records. Memory: 499.3MB
2025-07-16 13:15:11,714 - __main__ - INFO - Processed 45000 records. Memory: 510.4MB
2025-07-16 13:15:13,134 - __main__ - INFO - Processed 50000 records. Memory: 521.4MB
2025-07-16 13:15:14,443 - __main__ - INFO - Processed 55000 records. Memory: 531.9MB
2025-07-16 13:15:15,795 - __main__ - INFO - Processed 60000 records. Memory: 543.4MB
2025-07-16 13:15:17,143 - __main__ - INFO - Processed 65000 records. Memory: 555.3MB
2025-07-16 13:15:18,524 - __main__ - INFO - Processed 70000 records. Memory: 566.4MB
2025-07-16 13:15:19,721 - __main__ - INFO - Processed 75000 records. Memory: 577.7MB
2025-07-16 13:15:21,371 - __main__ - INFO - Processed 80000 records. Memory: 590.9MB
2025-07-16 13:15:22,969 - __main__ - INFO - Processed 85000 records. Memory: 601.7MB
2025-07-16 13:15:24,499 - __main__ - INFO - Processed 90000 records. Memory: 613.9MB
2025-07-16 13:15:26,196 - __main__ - INFO - Converting embeddings to numpy array...
2025-07-16 13:15:26,487 - __main__ - INFO - Embeddings array created. Memory: 624.1MB -> 704.6MB
2025-07-16 13:15:26,487 - __main__ - INFO - Loaded 94502 records with 384 dimensional embeddings (float16)
2025-07-16 13:15:26,488 - __main__ - INFO - Embeddings memory usage: 69.2 MB
2025-07-16 13:15:26,488 - __main__ - INFO - Initializing BM25 for lexical search...
2025-07-16 13:15:26,489 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 13:15:26,489 - __main__ - INFO - BM25 batch 1: documents 0-5000
2025-07-16 13:15:27,703 - __main__ - INFO - BM25 batch 6: documents 25000-30000
2025-07-16 13:15:29,844 - __main__ - INFO - BM25 batch 11: documents 50000-55000
2025-07-16 13:15:32,416 - __main__ - INFO - BM25 batch 16: documents 75000-80000
2025-07-16 13:15:34,852 - __main__ - INFO - Building term frequencies...
2025-07-16 13:15:35,093 - __main__ - INFO - Built term frequencies for 25000/94502 documents
2025-07-16 13:15:35,518 - __main__ - INFO - Built term frequencies for 50000/94502 documents
2025-07-16 13:15:35,965 - __main__ - INFO - Built term frequencies for 75000/94502 documents
2025-07-16 13:15:36,330 - __main__ - INFO - Building term document frequencies...
2025-07-16 13:15:37,476 - __main__ - INFO - Processed 50000/94502 documents for term doc freq
2025-07-16 13:15:39,075 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 13:15:39,473 - __main__ - INFO - BM25 initialized. Memory: 704.6MB -> 1834.8MB
2025-07-16 13:15:39,898 - __main__ - INFO - Cache loading complete. Final memory: 1834.8MB
2025-07-16 13:15:39,916 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 13:16:10,297 - __main__ - INFO - Request 1752686170-2673352798768: GET http://localhost:8000/docs
2025-07-16 13:16:10,298 - __main__ - INFO - Request 1752686170-2673352798768 completed in 1.00ms with status 200
2025-07-16 13:16:10,918 - __main__ - INFO - Request 1752686170-2673612867776: GET http://localhost:8000/openapi.json
2025-07-16 13:16:10,947 - __main__ - INFO - Request 1752686170-2673612867776 completed in 29.89ms with status 200
2025-07-16 13:16:29,159 - __main__ - INFO - Request 1752686189-2673352948480: GET http://localhost:8000/memory
2025-07-16 13:16:29,161 - __main__ - INFO - Request 1752686189-2673352948480 completed in 2.00ms with status 200
2025-07-16 13:17:32,290 - __main__ - INFO - Request 1752686252-2673352948624: GET http://localhost:8000/search?q=marijuana&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:17:32,291 - __main__ - INFO - Processing search request: 'marijuana' on table 'ReportNLP'
2025-07-16 13:17:32,291 - __main__ - INFO - Processing search: 'marijuana' (Memory: 1843.4MB)
2025-07-16 13:17:32,761 - __main__ - INFO - Search completed: 20 results in 469.31ms (Memory: 1843.4MB -> 1889.9MB, diff: +46.5MB)
2025-07-16 13:17:33,250 - __main__ - WARNING - Request 1752686252-2673352948624 increased memory by 46.5MB (from 1843.4MB to 1889.9MB)
2025-07-16 13:17:33,656 - __main__ - INFO - Request 1752686252-2673352948624 completed in 959.50ms with status 200
2025-07-16 13:18:04,205 - __main__ - INFO - Request 1752686284-2674081398656: GET http://localhost:8000/search?q=marijuana1&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:18:04,206 - __main__ - INFO - Processing search request: 'marijuana1' on table 'ReportNLP'
2025-07-16 13:18:04,207 - __main__ - INFO - Processing search: 'marijuana1' (Memory: 1889.9MB)
2025-07-16 13:18:04,639 - __main__ - INFO - Search completed: 20 results in 432.33ms (Memory: 1889.9MB -> 1890.7MB, diff: +0.7MB)
2025-07-16 13:18:04,642 - __main__ - INFO - Request 1752686284-2674081398656 completed in 435.93ms with status 200
2025-07-16 13:18:19,772 - __main__ - INFO - Request 1752686299-2673352790032: GET http://localhost:8000/search?q=marijuana2&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:18:19,773 - __main__ - INFO - Processing search request: 'marijuana2' on table 'ReportNLP'
2025-07-16 13:18:19,773 - __main__ - INFO - Processing search: 'marijuana2' (Memory: 1890.7MB)
2025-07-16 13:18:20,227 - __main__ - INFO - Search completed: 20 results in 453.86ms (Memory: 1890.7MB -> 1890.7MB, diff: +0.0MB)
2025-07-16 13:18:20,229 - __main__ - INFO - Request 1752686299-2673352790032 completed in 457.87ms with status 200
2025-07-16 13:18:47,486 - __main__ - INFO - Request 1752686327-2673352943392: POST http://localhost:8000/gc
2025-07-16 13:18:47,924 - __main__ - INFO - Request 1752686327-2673352943392 completed in 437.89ms with status 200
2025-07-16 13:19:47,104 - __main__ - INFO - Request 1752686387-2673352942288: POST http://localhost:8000/gc
2025-07-16 13:19:47,511 - __main__ - INFO - Request 1752686387-2673352942288 completed in 407.63ms with status 200
2025-07-16 13:20:09,895 - __main__ - INFO - Request 1752686409-2673352942912: GET http://localhost:8000/search?q=breakin&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:20:09,896 - __main__ - INFO - Processing search request: 'breakin' on table 'ReportNLP'
2025-07-16 13:20:09,896 - __main__ - INFO - Processing search: 'breakin' (Memory: 1890.8MB)
2025-07-16 13:20:10,361 - __main__ - INFO - Search completed: 20 results in 464.86ms (Memory: 1890.8MB -> 1890.8MB, diff: +0.1MB)
2025-07-16 13:20:10,364 - __main__ - INFO - Request 1752686409-2673352942912 completed in 468.86ms with status 200
2025-07-16 13:20:44,335 - __main__ - INFO - Request 1752686444-2673352794592: POST http://localhost:8000/gc
2025-07-16 13:20:44,751 - __main__ - INFO - Request 1752686444-2673352794592 completed in 416.07ms with status 200
2025-07-16 13:22:09,890 - __main__ - INFO - Shutting down Search API...
2025-07-16 13:22:09,893 - __main__ - INFO - Database connection closed
2025-07-16 13:46:12,949 - __main__ - INFO - Starting up Search API...
2025-07-16 13:46:12,950 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 13:46:12,953 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 13:46:12,953 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 13:46:15,709 - __main__ - INFO - Model loaded successfully
2025-07-16 13:46:15,709 - __main__ - INFO - Initializing database connection...
2025-07-16 13:46:15,820 - __main__ - INFO - Database connection established
2025-07-16 13:46:15,821 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-16 13:46:15,821 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-16 13:46:15,821 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-16 13:47:02,898 - __main__ - INFO - ConnectorX loaded 94502 records in 47.08s
2025-07-16 13:47:02,904 - __main__ - INFO - Processing embeddings...
2025-07-16 13:47:18,160 - __main__ - INFO - Processed 94502 valid records with 384D embeddings
2025-07-16 13:47:18,160 - __main__ - INFO - Initializing BM25...
2025-07-16 13:47:18,161 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 13:47:18,161 - __main__ - INFO - BM25 batch 1: documents 0-5000
2025-07-16 13:47:19,427 - __main__ - INFO - BM25 batch 6: documents 25000-30000
2025-07-16 13:47:21,612 - __main__ - INFO - BM25 batch 11: documents 50000-55000
2025-07-16 13:47:23,854 - __main__ - INFO - BM25 batch 16: documents 75000-80000
2025-07-16 13:47:25,898 - __main__ - INFO - Building term frequencies...
2025-07-16 13:47:26,102 - __main__ - INFO - Built term frequencies for 25000/94502 documents
2025-07-16 13:47:26,568 - __main__ - INFO - Built term frequencies for 50000/94502 documents
2025-07-16 13:47:27,035 - __main__ - INFO - Built term frequencies for 75000/94502 documents
2025-07-16 13:47:27,397 - __main__ - INFO - Building term document frequencies...
2025-07-16 13:47:28,666 - __main__ - INFO - Processed 50000/94502 documents for term doc freq
2025-07-16 13:47:30,389 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 13:47:31,281 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 13:48:41,204 - __main__ - INFO - Request 1752688121-2524445814768: GET http://localhost:8000/search?q=breakin&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:48:41,205 - __main__ - INFO - Processing search request: 'breakin' on table 'ReportNLP'
2025-07-16 13:48:41,206 - __main__ - INFO - Processing search: 'breakin' (Memory: 1797.9MB)
2025-07-16 13:48:41,721 - __main__ - INFO - Search completed: 20 results in 516.03ms (Memory: 1797.9MB -> 1844.0MB, diff: +46.2MB)
2025-07-16 13:48:42,299 - __main__ - WARNING - Request 1752688121-2524445814768 increased memory by 46.3MB (from 1797.8MB to 1844.1MB)
2025-07-16 13:48:42,887 - __main__ - INFO - Request 1752688121-2524445814768 completed in 1095.06ms with status 200
2025-07-16 13:50:35,280 - __main__ - INFO - Shutting down Search API...
2025-07-16 13:50:35,282 - __main__ - INFO - Database connection closed
2025-07-16 13:54:32,525 - __main__ - INFO - Starting up Search API...
2025-07-16 13:54:32,525 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 13:54:32,528 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 13:54:32,528 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 13:54:35,063 - __main__ - INFO - Model loaded successfully
2025-07-16 13:54:35,063 - __main__ - INFO - Initializing database connection...
2025-07-16 13:54:35,175 - __main__ - INFO - Database connection established
2025-07-16 13:54:35,176 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-16 13:54:35,176 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-16 13:54:35,176 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-16 13:55:20,236 - __main__ - INFO - ConnectorX loaded 94502 records in 45.06s
2025-07-16 13:55:20,241 - __main__ - INFO - Processing embeddings...
2025-07-16 13:55:34,039 - __main__ - INFO - Processed 94502 valid records with 384D embeddings
2025-07-16 13:55:34,040 - __main__ - INFO - Initializing BM25...
2025-07-16 13:55:34,040 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 13:55:34,041 - __main__ - INFO - BM25 batch 1: documents 0-5000
2025-07-16 13:55:35,196 - __main__ - INFO - BM25 batch 6: documents 25000-30000
2025-07-16 13:55:37,210 - __main__ - INFO - BM25 batch 11: documents 50000-55000
2025-07-16 13:55:39,475 - __main__ - INFO - BM25 batch 16: documents 75000-80000
2025-07-16 13:55:41,389 - __main__ - INFO - Building term frequencies...
2025-07-16 13:55:41,601 - __main__ - INFO - Built term frequencies for 25000/94502 documents
2025-07-16 13:55:41,990 - __main__ - INFO - Built term frequencies for 50000/94502 documents
2025-07-16 13:55:42,411 - __main__ - INFO - Built term frequencies for 75000/94502 documents
2025-07-16 13:55:42,731 - __main__ - INFO - Building term document frequencies...
2025-07-16 13:55:43,876 - __main__ - INFO - Processed 50000/94502 documents for term doc freq
2025-07-16 13:55:45,690 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 13:55:46,598 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 13:57:24,934 - __main__ - INFO - Request 1752688644-2384866336480: GET http://localhost:8000/memory
2025-07-16 13:57:24,935 - __main__ - INFO - Request 1752688644-2384866336480 completed in 1.00ms with status 200
2025-07-16 13:58:30,842 - __main__ - INFO - Request 1752688710-2381770472528: GET http://localhost:8000/search?q=breakin&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 13:58:30,843 - __main__ - INFO - Processing search request: 'breakin' on table 'ReportNLP'
2025-07-16 13:58:30,843 - __main__ - INFO - Processing search: 'breakin' (Memory: 1798.3MB)
2025-07-16 13:58:31,299 - __main__ - INFO - Search completed: 20 results in 455.47ms (Memory: 1798.3MB -> 1844.7MB, diff: +46.4MB)
2025-07-16 13:58:31,744 - __main__ - WARNING - Request 1752688710-2381770472528 increased memory by 46.6MB (from 1798.2MB to 1844.8MB)
2025-07-16 13:58:32,134 - __main__ - INFO - Request 1752688710-2381770472528 completed in 901.50ms with status 200
2025-07-16 14:00:14,049 - __main__ - INFO - Shutting down Search API...
2025-07-16 14:00:14,051 - __main__ - INFO - Database connection closed
2025-07-16 14:04:22,022 - __main__ - INFO - Starting up Search API...
2025-07-16 14:04:22,023 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 14:04:22,026 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 14:04:22,026 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 14:04:24,446 - __main__ - INFO - Model loaded successfully
2025-07-16 14:04:24,446 - __main__ - INFO - Initializing database connection...
2025-07-16 14:04:24,575 - __main__ - INFO - Database connection established
2025-07-16 14:04:24,575 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-16 14:04:24,576 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-16 14:04:24,576 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-16 14:05:12,435 - __main__ - INFO - ConnectorX loaded 94502 records in 47.86s
2025-07-16 14:05:12,439 - __main__ - INFO - Processing embeddings...
2025-07-16 14:05:26,303 - __main__ - INFO - Processed 94502 valid records with 384D embeddings
2025-07-16 14:05:26,303 - __main__ - INFO - Initializing BM25...
2025-07-16 14:05:26,303 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 14:05:26,304 - __main__ - INFO - BM25 batch 1: documents 0-5000
2025-07-16 14:05:27,369 - __main__ - INFO - BM25 batch 6: documents 25000-30000
2025-07-16 14:05:29,241 - __main__ - INFO - BM25 batch 11: documents 50000-55000
2025-07-16 14:05:31,437 - __main__ - INFO - BM25 batch 16: documents 75000-80000
2025-07-16 14:05:33,345 - __main__ - INFO - Building term frequencies...
2025-07-16 14:05:33,552 - __main__ - INFO - Built term frequencies for 25000/94502 documents
2025-07-16 14:05:33,922 - __main__ - INFO - Built term frequencies for 50000/94502 documents
2025-07-16 14:05:34,338 - __main__ - INFO - Built term frequencies for 75000/94502 documents
2025-07-16 14:05:34,662 - __main__ - INFO - Building term document frequencies...
2025-07-16 14:05:35,717 - __main__ - INFO - Processed 50000/94502 documents for term doc freq
2025-07-16 14:05:37,125 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 14:05:38,146 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 14:05:55,261 - __main__ - INFO - Request 1752689155-1751358070352: GET http://localhost:8000/search?q=breakin&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-16 14:05:55,262 - __main__ - INFO - Processing search request: 'breakin' on table 'ReportNLP'
2025-07-16 14:05:55,263 - __main__ - INFO - Processing search: 'breakin' (Memory: 1795.1MB)
2025-07-16 14:05:55,690 - __main__ - INFO - Search completed: 20 results in 428.68ms (Memory: 1795.1MB -> 1841.4MB, diff: +46.3MB)
2025-07-16 14:05:56,107 - __main__ - WARNING - Request 1752689155-1751358070352 increased memory by 46.4MB (from 1795.0MB to 1841.5MB)
2025-07-16 14:05:56,508 - __main__ - INFO - Request 1752689155-1751358070352 completed in 845.87ms with status 200
2025-07-16 14:07:31,921 - __main__ - INFO - Request 1752689251-1749089979856: GET http://localhost:8000/memory
2025-07-16 14:07:31,922 - __main__ - INFO - Request 1752689251-1749089979856 completed in 1.00ms with status 200
2025-07-16 14:08:03,983 - __main__ - INFO - Request 1752689283-1749089984080: POST http://localhost:8000/gc
2025-07-16 14:08:04,426 - __main__ - INFO - Request 1752689283-1749089984080 completed in 442.61ms with status 200
2025-07-16 14:08:07,746 - __main__ - INFO - Request 1752689287-1751358072032: GET http://localhost:8000/memory
2025-07-16 14:08:07,747 - __main__ - INFO - Request 1752689287-1751358072032 completed in 1.00ms with status 200
2025-07-16 14:08:20,085 - __main__ - INFO - Request 1752689300-1751358072608: GET http://localhost:8000/docs
2025-07-16 14:08:20,086 - __main__ - INFO - Request 1752689300-1751358072608 completed in 1.00ms with status 200
2025-07-16 14:08:20,483 - __main__ - INFO - Request 1752689300-1749089981344: GET http://localhost:8000/openapi.json
2025-07-16 14:08:20,502 - __main__ - INFO - Request 1752689300-1749089981344 completed in 18.68ms with status 200
2025-07-16 14:08:51,835 - __main__ - INFO - Request 1752689331-1749089985424: POST http://localhost:8000/gc
2025-07-16 14:08:52,237 - __main__ - INFO - Request 1752689331-1749089985424 completed in 402.11ms with status 200
2025-07-16 14:09:14,065 - __main__ - INFO - Request 1752689354-1749089990272: GET http://localhost:8000/memory
2025-07-16 14:09:14,067 - __main__ - INFO - Request 1752689354-1749089990272 completed in 1.00ms with status 200
2025-07-16 14:09:26,557 - __main__ - INFO - Request 1752689366-1749089982928: GET http://localhost:8000/
2025-07-16 14:09:26,558 - __main__ - INFO - Request 1752689366-1749089982928 completed in 1.00ms with status 200
2025-07-16 14:10:01,884 - __main__ - INFO - Shutting down Search API...
2025-07-16 14:10:01,899 - __main__ - INFO - Database connection closed
2025-07-16 14:27:56,628 - __main__ - INFO - Starting up Search API...
2025-07-16 14:27:56,629 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 14:27:56,633 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 14:27:56,633 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 14:27:59,332 - __main__ - INFO - Model loaded successfully
2025-07-16 14:27:59,333 - __main__ - INFO - Initializing database connection...
2025-07-16 14:27:59,448 - __main__ - INFO - Database connection established
2025-07-16 14:27:59,449 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-16 14:27:59,449 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-16 14:27:59,449 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-16 14:28:45,990 - __main__ - INFO - ConnectorX loaded 94502 records in 46.54s
2025-07-16 14:28:45,994 - __main__ - INFO - Processing embeddings...
2025-07-16 14:29:00,585 - __main__ - INFO - Processed 94502 valid records with 384D embeddings
2025-07-16 14:29:00,585 - __main__ - INFO - Initializing DiskBM25...
2025-07-16 14:29:00,603 - __main__ - INFO - Building disk-based BM25 for 94502 documents...
2025-07-16 14:29:00,881 - __main__ - INFO - Processed batch 1: documents 0-1000
2025-07-16 14:29:01,344 - __main__ - INFO - Processed batch 2: documents 1000-2000
2025-07-16 14:29:01,774 - __main__ - INFO - Processed batch 3: documents 2000-3000
2025-07-16 14:29:02,354 - __main__ - INFO - Processed batch 4: documents 3000-4000
2025-07-16 14:29:02,999 - __main__ - INFO - Processed batch 5: documents 4000-5000
2025-07-16 14:29:03,889 - __main__ - INFO - Processed batch 6: documents 5000-6000
2025-07-16 14:29:04,726 - __main__ - INFO - Processed batch 7: documents 6000-7000
2025-07-16 14:29:05,527 - __main__ - INFO - Processed batch 8: documents 7000-8000
2025-07-16 14:29:06,195 - __main__ - INFO - Processed batch 9: documents 8000-9000
2025-07-16 14:29:06,912 - __main__ - INFO - Processed batch 10: documents 9000-10000
2025-07-16 14:29:07,773 - __main__ - INFO - Processed batch 11: documents 10000-11000
2025-07-16 14:29:08,792 - __main__ - INFO - Processed batch 12: documents 11000-12000
2025-07-16 14:29:09,698 - __main__ - INFO - Processed batch 13: documents 12000-13000
2025-07-16 14:29:10,690 - __main__ - INFO - Processed batch 14: documents 13000-14000
2025-07-16 14:29:11,743 - __main__ - INFO - Processed batch 15: documents 14000-15000
2025-07-16 14:29:12,819 - __main__ - INFO - Processed batch 16: documents 15000-16000
2025-07-16 14:29:14,126 - __main__ - INFO - Processed batch 17: documents 16000-17000
2025-07-16 14:29:15,361 - __main__ - INFO - Processed batch 18: documents 17000-18000
2025-07-16 14:29:16,709 - __main__ - INFO - Processed batch 19: documents 18000-19000
2025-07-16 14:29:18,037 - __main__ - INFO - Processed batch 20: documents 19000-20000
2025-07-16 14:29:19,378 - __main__ - INFO - Processed batch 21: documents 20000-21000
2025-07-16 14:29:20,817 - __main__ - INFO - Processed batch 22: documents 21000-22000
2025-07-16 14:29:22,239 - __main__ - INFO - Processed batch 23: documents 22000-23000
2025-07-16 14:29:23,850 - __main__ - INFO - Processed batch 24: documents 23000-24000
2025-07-16 14:29:25,392 - __main__ - INFO - Processed batch 25: documents 24000-25000
2025-07-16 14:29:27,282 - __main__ - INFO - Processed batch 26: documents 25000-26000
2025-07-16 14:29:29,025 - __main__ - INFO - Processed batch 27: documents 26000-27000
2025-07-16 14:29:30,517 - __main__ - INFO - Processed batch 28: documents 27000-28000
2025-07-16 14:29:32,326 - __main__ - INFO - Processed batch 29: documents 28000-29000
2025-07-16 14:29:34,187 - __main__ - INFO - Processed batch 30: documents 29000-30000
2025-07-16 14:29:36,038 - __main__ - INFO - Processed batch 31: documents 30000-31000
2025-07-16 14:29:38,307 - __main__ - INFO - Processed batch 32: documents 31000-32000
2025-07-16 14:29:40,514 - __main__ - INFO - Processed batch 33: documents 32000-33000
2025-07-16 14:29:42,295 - __main__ - INFO - Processed batch 34: documents 33000-34000
2025-07-16 14:29:44,488 - __main__ - INFO - Processed batch 35: documents 34000-35000
2025-07-16 14:29:47,012 - __main__ - INFO - Processed batch 36: documents 35000-36000
2025-07-16 14:29:49,774 - __main__ - INFO - Processed batch 37: documents 36000-37000
2025-07-16 14:29:52,137 - __main__ - INFO - Processed batch 38: documents 37000-38000
2025-07-16 14:29:54,631 - __main__ - INFO - Processed batch 39: documents 38000-39000
2025-07-16 14:29:57,261 - __main__ - INFO - Processed batch 40: documents 39000-40000
2025-07-16 14:29:59,980 - __main__ - INFO - Processed batch 41: documents 40000-41000
2025-07-16 14:30:02,837 - __main__ - INFO - Processed batch 42: documents 41000-42000
2025-07-16 14:30:05,386 - __main__ - INFO - Processed batch 43: documents 42000-43000
2025-07-16 14:30:08,494 - __main__ - INFO - Processed batch 44: documents 43000-44000
2025-07-16 14:30:11,003 - __main__ - INFO - Processed batch 45: documents 44000-45000
2025-07-16 14:30:13,541 - __main__ - INFO - Processed batch 46: documents 45000-46000
2025-07-16 14:30:16,628 - __main__ - INFO - Processed batch 47: documents 46000-47000
2025-07-16 14:30:19,725 - __main__ - INFO - Processed batch 48: documents 47000-48000
2025-07-16 14:30:22,779 - __main__ - INFO - Processed batch 49: documents 48000-49000
2025-07-16 14:30:26,976 - __main__ - INFO - Processed batch 50: documents 49000-50000
2025-07-16 14:30:30,246 - __main__ - INFO - Processed batch 51: documents 50000-51000
2025-07-16 14:30:33,408 - __main__ - INFO - Processed batch 52: documents 51000-52000
2025-07-16 14:30:36,211 - __main__ - INFO - Processed batch 53: documents 52000-53000
2025-07-16 14:30:39,288 - __main__ - INFO - Processed batch 54: documents 53000-54000
2025-07-16 14:30:41,901 - __main__ - INFO - Processed batch 55: documents 54000-55000
2025-07-16 14:30:44,640 - __main__ - INFO - Processed batch 56: documents 55000-56000
2025-07-16 14:30:47,788 - __main__ - INFO - Processed batch 57: documents 56000-57000
2025-07-16 14:30:50,452 - __main__ - INFO - Processed batch 58: documents 57000-58000
2025-07-16 14:30:53,450 - __main__ - INFO - Processed batch 59: documents 58000-59000
2025-07-16 14:30:56,861 - __main__ - INFO - Processed batch 60: documents 59000-60000
2025-07-16 14:31:00,620 - __main__ - INFO - Processed batch 61: documents 60000-61000
2025-07-16 14:31:03,923 - __main__ - INFO - Processed batch 62: documents 61000-62000
2025-07-16 14:31:07,116 - __main__ - INFO - Processed batch 63: documents 62000-63000
2025-07-16 14:31:10,600 - __main__ - INFO - Processed batch 64: documents 63000-64000
2025-07-16 14:31:14,470 - __main__ - INFO - Processed batch 65: documents 64000-65000
2025-07-16 14:31:17,862 - __main__ - INFO - Processed batch 66: documents 65000-66000
2025-07-16 14:31:21,390 - __main__ - INFO - Processed batch 67: documents 66000-67000
2025-07-16 14:31:24,802 - __main__ - INFO - Processed batch 68: documents 67000-68000
2025-07-16 14:31:27,712 - __main__ - INFO - Processed batch 69: documents 68000-69000
2025-07-16 14:31:31,659 - __main__ - INFO - Processed batch 70: documents 69000-70000
2025-07-16 14:31:35,150 - __main__ - INFO - Processed batch 71: documents 70000-71000
2025-07-16 14:31:39,016 - __main__ - INFO - Processed batch 72: documents 71000-72000
2025-07-16 14:31:42,468 - __main__ - INFO - Processed batch 73: documents 72000-73000
2025-07-16 14:31:46,018 - __main__ - INFO - Processed batch 74: documents 73000-74000
2025-07-16 14:31:49,692 - __main__ - INFO - Processed batch 75: documents 74000-75000
2025-07-16 14:31:53,289 - __main__ - INFO - Processed batch 76: documents 75000-76000
2025-07-16 14:31:57,198 - __main__ - INFO - Processed batch 77: documents 76000-77000
2025-07-16 14:32:01,471 - __main__ - INFO - Processed batch 78: documents 77000-78000
2025-07-16 14:32:05,140 - __main__ - INFO - Processed batch 79: documents 78000-79000
2025-07-16 14:32:09,027 - __main__ - INFO - Processed batch 80: documents 79000-80000
2025-07-16 14:32:12,837 - __main__ - INFO - Processed batch 81: documents 80000-81000
2025-07-16 14:32:17,133 - __main__ - INFO - Processed batch 82: documents 81000-82000
2025-07-16 14:32:22,777 - __main__ - INFO - Processed batch 83: documents 82000-83000
2025-07-16 14:32:26,975 - __main__ - INFO - Processed batch 84: documents 83000-84000
2025-07-16 14:32:30,933 - __main__ - INFO - Processed batch 85: documents 84000-85000
2025-07-16 14:32:35,383 - __main__ - INFO - Processed batch 86: documents 85000-86000
2025-07-16 14:32:40,259 - __main__ - INFO - Processed batch 87: documents 86000-87000
2025-07-16 14:32:45,207 - __main__ - INFO - Processed batch 88: documents 87000-88000
2025-07-16 14:32:50,202 - __main__ - INFO - Processed batch 89: documents 88000-89000
2025-07-16 14:32:56,080 - __main__ - INFO - Processed batch 90: documents 89000-90000
2025-07-16 14:33:03,634 - __main__ - INFO - Processed batch 91: documents 90000-91000
2025-07-16 14:33:10,188 - __main__ - INFO - Processed batch 92: documents 91000-92000
2025-07-16 14:33:16,504 - __main__ - INFO - Processed batch 93: documents 92000-93000
2025-07-16 14:33:22,933 - __main__ - INFO - Processed batch 94: documents 93000-94000
2025-07-16 14:33:26,488 - __main__ - INFO - Processed batch 95: documents 94000-94502
2025-07-16 14:33:26,493 - __main__ - INFO - Disk BM25 complete. Vocab: 206876 terms, Memory usage: minimal
2025-07-16 14:33:26,892 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 14:47:32,050 - __main__ - INFO - Starting up Search API...
2025-07-16 14:47:32,051 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 14:47:32,055 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 14:47:32,055 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 14:47:34,778 - __main__ - INFO - Model loaded successfully
2025-07-16 14:47:34,778 - __main__ - INFO - Initializing database connection...
2025-07-16 14:47:34,905 - __main__ - INFO - Database connection established
2025-07-16 14:47:34,905 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-16 14:47:34,906 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-16 14:47:34,906 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-16 14:48:24,824 - __main__ - INFO - ConnectorX loaded 94502 records in 49.92s
2025-07-16 14:48:24,828 - __main__ - INFO - Processing embeddings...
2025-07-16 14:48:39,515 - __main__ - INFO - Processed 94502 valid records with 384D embeddings
2025-07-16 14:48:39,515 - __main__ - INFO - Initializing OptimizedBM25...
2025-07-16 14:48:39,516 - __main__ - INFO - Building optimized BM25 for 94502 documents...
2025-07-16 14:48:39,516 - __main__ - INFO - Processing document 0/94502
2025-07-16 14:48:40,018 - __main__ - INFO - Processing document 10000/94502
2025-07-16 14:48:40,869 - __main__ - INFO - Processing document 20000/94502
2025-07-16 14:48:41,814 - __main__ - INFO - Processing document 30000/94502
2025-07-16 14:48:43,061 - __main__ - INFO - Processing document 40000/94502
2025-07-16 14:48:44,482 - __main__ - INFO - Processing document 50000/94502
2025-07-16 14:48:45,813 - __main__ - INFO - Processing document 60000/94502
2025-07-16 14:48:47,300 - __main__ - INFO - Processing document 70000/94502
2025-07-16 14:48:48,890 - __main__ - INFO - Processing document 80000/94502
2025-07-16 14:48:50,364 - __main__ - INFO - Processing document 90000/94502
2025-07-16 14:48:51,217 - __main__ - INFO - Found 8935 common terms (>= 50 docs)
2025-07-16 14:48:51,218 - __main__ - INFO - Total vocabulary: 206876 terms
2025-07-16 14:54:37,779 - __main__ - INFO - Optimized BM25 complete. Pre-computed: 8935 terms
2025-07-16 14:54:37,936 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 14:55:11,164 - __main__ - INFO - Request 1752692111-2461803130192: GET http://localhost:8000/memory
2025-07-16 14:55:11,164 - __main__ - INFO - Request 1752692111-2461803130192 completed in 1.00ms with status 200
2025-07-16 14:55:30,278 - __main__ - INFO - Request 1752692130-2463322989120: GET http://localhost:8000/docs
2025-07-16 14:55:30,279 - __main__ - INFO - Request 1752692130-2463322989120 completed in 1.02ms with status 200
2025-07-16 14:55:30,675 - __main__ - INFO - Request 1752692130-2463322987728: GET http://localhost:8000/openapi.json
2025-07-16 14:55:30,690 - __main__ - INFO - Request 1752692130-2463322987728 completed in 15.04ms with status 200
2025-07-16 14:55:38,690 - __main__ - INFO - Request 1752692138-2463322975904: POST http://localhost:8000/gc
2025-07-16 14:55:38,839 - __main__ - INFO - Request 1752692138-2463322975904 completed in 148.65ms with status 200
2025-07-16 14:56:51,765 - __main__ - INFO - Shutting down Search API...
2025-07-16 14:56:51,768 - __main__ - INFO - Database connection closed
2025-07-16 15:00:40,545 - __main__ - INFO - Starting up Search API...
2025-07-16 15:00:40,546 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-16 15:00:40,549 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-16 15:00:40,549 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-16 15:00:43,097 - __main__ - INFO - Model loaded successfully
2025-07-16 15:00:43,098 - __main__ - INFO - Initializing database connection...
2025-07-16 15:00:43,210 - __main__ - INFO - Database connection established
2025-07-16 15:00:43,210 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-16 15:00:43,211 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-16 15:00:43,211 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-16 15:01:33,883 - __main__ - INFO - ConnectorX loaded 94502 records in 50.67s
2025-07-16 15:01:33,887 - __main__ - INFO - Processing embeddings...
2025-07-16 15:01:50,442 - __main__ - INFO - Processed 94502 valid records with 384D embeddings
2025-07-16 15:01:50,442 - __main__ - INFO - Initializing BM25...
2025-07-16 15:01:50,443 - __main__ - INFO - Preprocessing 94502 documents for BM25...
2025-07-16 15:01:50,443 - __main__ - INFO - BM25 batch 1: documents 0-5000
2025-07-16 15:01:51,606 - __main__ - INFO - BM25 batch 6: documents 25000-30000
2025-07-16 15:01:53,736 - __main__ - INFO - BM25 batch 11: documents 50000-55000
2025-07-16 15:01:56,149 - __main__ - INFO - BM25 batch 16: documents 75000-80000
2025-07-16 15:01:58,327 - __main__ - INFO - Building term frequencies...
2025-07-16 15:01:58,561 - __main__ - INFO - Built term frequencies for 25000/94502 documents
2025-07-16 15:01:58,961 - __main__ - INFO - Built term frequencies for 50000/94502 documents
2025-07-16 15:01:59,419 - __main__ - INFO - Built term frequencies for 75000/94502 documents
2025-07-16 15:01:59,757 - __main__ - INFO - Building term document frequencies...
2025-07-16 15:02:00,984 - __main__ - INFO - Processed 50000/94502 documents for term doc freq
2025-07-16 15:02:02,504 - __main__ - INFO - BM25 initialization complete. Vocabulary size: 206876
2025-07-16 15:02:03,393 - __main__ - INFO - Search cache loaded: 94502 records
2025-07-16 15:19:32,469 - __main__ - INFO - Shutting down Search API...
2025-07-16 15:19:32,485 - __main__ - INFO - Database connection closed
2025-07-18 10:02:31,175 - __main__ - INFO - Starting up Search API...
2025-07-18 10:02:31,176 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-18 10:02:31,179 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 10:02:31,179 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-18 10:02:34,428 - __main__ - INFO - Model loaded successfully
2025-07-18 10:02:34,428 - __main__ - INFO - Initializing database connection...
2025-07-18 10:02:34,575 - __main__ - INFO - Database connection established
2025-07-18 10:02:34,575 - __main__ - INFO - Loading search cache with ConnectorX...
2025-07-18 10:02:34,575 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-18 10:02:34,575 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-18 10:10:09,244 - __main__ - INFO - ConnectorX loaded 690334 records in 454.67s
2025-07-18 10:10:09,344 - __main__ - INFO - Processing embeddings...
2025-07-18 10:13:15,532 - __main__ - INFO - Processed 690334 valid records with 384D embeddings
2025-07-18 10:13:15,534 - __main__ - INFO - Initializing BM25...
2025-07-18 10:13:15,534 - __main__ - INFO - Preprocessing 690334 documents for BM25...
2025-07-18 10:13:15,536 - __main__ - INFO - BM25 batch 1: documents 0-5000
2025-07-18 10:13:17,536 - __main__ - INFO - BM25 batch 6: documents 25000-30000
2025-07-18 10:13:20,498 - __main__ - INFO - BM25 batch 11: documents 50000-55000
2025-07-18 10:13:23,853 - __main__ - INFO - BM25 batch 16: documents 75000-80000
2025-07-18 10:13:26,935 - __main__ - INFO - BM25 batch 21: documents 100000-105000
2025-07-18 10:13:30,518 - __main__ - INFO - BM25 batch 26: documents 125000-130000
2025-07-18 10:13:34,075 - __main__ - INFO - BM25 batch 31: documents 150000-155000
2025-07-18 10:13:37,557 - __main__ - INFO - BM25 batch 36: documents 175000-180000
2025-07-18 10:13:41,740 - __main__ - INFO - BM25 batch 41: documents 200000-205000
2025-07-18 10:13:45,586 - __main__ - INFO - BM25 batch 46: documents 225000-230000
2025-07-18 10:13:49,604 - __main__ - INFO - BM25 batch 51: documents 250000-255000
2025-07-18 10:13:54,266 - __main__ - INFO - BM25 batch 56: documents 275000-280000
2025-07-18 10:13:58,646 - __main__ - INFO - BM25 batch 61: documents 300000-305000
2025-07-18 10:14:03,843 - __main__ - INFO - BM25 batch 66: documents 325000-330000
2025-07-18 10:14:08,821 - __main__ - INFO - BM25 batch 71: documents 350000-355000
2025-07-18 10:14:14,087 - __main__ - INFO - BM25 batch 76: documents 375000-380000
2025-07-18 10:14:19,201 - __main__ - INFO - BM25 batch 81: documents 400000-405000
2025-07-18 10:14:24,884 - __main__ - INFO - BM25 batch 86: documents 425000-430000
2025-07-18 10:14:30,594 - __main__ - INFO - BM25 batch 91: documents 450000-455000
2025-07-18 10:14:36,480 - __main__ - INFO - BM25 batch 96: documents 475000-480000
2025-07-18 10:14:45,129 - __main__ - INFO - BM25 batch 101: documents 500000-505000
2025-07-18 10:14:54,174 - __main__ - INFO - BM25 batch 106: documents 525000-530000
2025-07-18 10:15:08,755 - __main__ - INFO - BM25 batch 111: documents 550000-555000
2025-07-18 10:15:18,372 - __main__ - INFO - BM25 batch 116: documents 575000-580000
2025-07-18 10:15:30,596 - __main__ - INFO - BM25 batch 121: documents 600000-605000
2025-07-18 10:15:42,203 - __main__ - INFO - BM25 batch 126: documents 625000-630000
2025-07-18 10:15:53,493 - __main__ - INFO - BM25 batch 131: documents 650000-655000
2025-07-18 10:16:05,456 - __main__ - INFO - BM25 batch 136: documents 675000-680000
2025-07-18 10:16:12,773 - __main__ - INFO - Building term frequencies...
2025-07-18 10:16:13,224 - __main__ - INFO - Built term frequencies for 25000/690334 documents
2025-07-18 10:16:14,373 - __main__ - INFO - Built term frequencies for 50000/690334 documents
2025-07-18 10:16:16,416 - __main__ - INFO - Built term frequencies for 75000/690334 documents
2025-07-18 10:16:18,586 - __main__ - INFO - Built term frequencies for 100000/690334 documents
2025-07-18 10:16:26,773 - __main__ - INFO - Built term frequencies for 125000/690334 documents
2025-07-18 10:16:27,676 - __main__ - INFO - Built term frequencies for 150000/690334 documents
2025-07-18 10:16:28,518 - __main__ - INFO - Built term frequencies for 175000/690334 documents
2025-07-18 10:16:31,059 - __main__ - ERROR - ConnectorX cache loading failed: 
2025-07-18 10:16:31,059 - __main__ - INFO - Falling back to SQLAlchemy method...
2025-07-18 10:16:31,059 - __main__ - INFO - Loading search data into memory with SQLAlchemy (fallback)...
2025-07-18 10:16:31,134 - __main__ - INFO - Processing records...
2025-07-18 10:16:31,141 - __main__ - ERROR - Failed to load search cache: 
2025-07-18 10:16:38,182 - __main__ - ERROR - Startup failed: Cache loading failed: 
2025-07-18 14:13:36,044 - __main__ - INFO - Starting up Search API...
2025-07-18 14:13:36,045 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-18 14:13:36,057 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 14:13:36,057 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-18 14:13:40,421 - __main__ - INFO - Model loaded successfully
2025-07-18 14:13:40,422 - __main__ - INFO - Initializing database connection...
2025-07-18 14:13:40,611 - __main__ - INFO - Database connection established
2025-07-18 14:13:40,611 - __main__ - INFO - Loading memory-mapped search cache...
2025-07-18 14:13:40,612 - __main__ - INFO - Initializing memory-mapped search cache...
2025-07-18 14:13:40,613 - __main__ - WARNING - Cache doesn't exist, building from database...
2025-07-18 14:13:40,614 - __main__ - INFO - Building memory-mapped cache from database (streaming to disk)...
2025-07-18 14:13:40,615 - __main__ - INFO - Counting total records...
2025-07-18 14:13:42,967 - __main__ - INFO - Found 701370 records to process
2025-07-18 14:13:45,281 - __main__ - INFO - Processing batch 1: 5000 records
2025-07-18 14:13:46,364 - __main__ - INFO - Wrote 5000 records to disk (total: 5000)
2025-07-18 14:13:48,782 - __main__ - INFO - Processing batch 2: 5000 records
2025-07-18 14:13:50,062 - __main__ - INFO - Wrote 5000 records to disk (total: 10000)
2025-07-18 14:13:52,407 - __main__ - INFO - Processing batch 3: 5000 records
2025-07-18 14:13:53,516 - __main__ - INFO - Wrote 5000 records to disk (total: 15000)
2025-07-18 14:13:56,107 - __main__ - INFO - Processing batch 4: 5000 records
2025-07-18 14:13:57,114 - __main__ - INFO - Wrote 5000 records to disk (total: 20000)
2025-07-18 14:13:59,673 - __main__ - INFO - Processing batch 5: 5000 records
2025-07-18 14:14:00,818 - __main__ - INFO - Wrote 5000 records to disk (total: 25000)
2025-07-18 14:14:03,437 - __main__ - INFO - Processing batch 6: 5000 records
2025-07-18 14:14:04,705 - __main__ - INFO - Wrote 5000 records to disk (total: 30000)
2025-07-18 14:14:07,965 - __main__ - INFO - Processing batch 7: 5000 records
2025-07-18 14:14:09,184 - __main__ - INFO - Wrote 5000 records to disk (total: 35000)
2025-07-18 14:14:11,858 - __main__ - INFO - Processing batch 8: 5000 records
2025-07-18 14:14:12,978 - __main__ - INFO - Wrote 5000 records to disk (total: 40000)
2025-07-18 14:14:15,725 - __main__ - INFO - Processing batch 9: 5000 records
2025-07-18 14:14:16,774 - __main__ - INFO - Wrote 5000 records to disk (total: 45000)
2025-07-18 14:14:19,391 - __main__ - INFO - Processing batch 10: 5000 records
2025-07-18 14:14:20,505 - __main__ - INFO - Wrote 5000 records to disk (total: 50000)
2025-07-18 14:14:20,646 - __main__ - INFO - GC freed 0.0MB after 50000 records
2025-07-18 14:14:23,499 - __main__ - INFO - Processing batch 11: 5000 records
2025-07-18 14:14:24,511 - __main__ - INFO - Wrote 5000 records to disk (total: 55000)
2025-07-18 14:14:27,287 - __main__ - INFO - Processing batch 12: 5000 records
2025-07-18 14:14:28,406 - __main__ - INFO - Wrote 5000 records to disk (total: 60000)
2025-07-18 14:14:31,602 - __main__ - INFO - Processing batch 13: 5000 records
2025-07-18 14:14:32,699 - __main__ - INFO - Wrote 5000 records to disk (total: 65000)
2025-07-18 14:14:35,513 - __main__ - INFO - Processing batch 14: 5000 records
2025-07-18 14:14:36,716 - __main__ - INFO - Wrote 5000 records to disk (total: 70000)
2025-07-18 14:14:40,223 - __main__ - INFO - Processing batch 15: 5000 records
2025-07-18 14:14:41,384 - __main__ - INFO - Wrote 5000 records to disk (total: 75000)
2025-07-18 14:14:44,473 - __main__ - INFO - Processing batch 16: 5000 records
2025-07-18 14:14:45,705 - __main__ - INFO - Wrote 5000 records to disk (total: 80000)
2025-07-18 14:14:48,907 - __main__ - INFO - Processing batch 17: 5000 records
2025-07-18 14:14:49,935 - __main__ - INFO - Wrote 5000 records to disk (total: 85000)
2025-07-18 14:14:54,523 - __main__ - INFO - Processing batch 18: 5000 records
2025-07-18 14:14:55,492 - __main__ - INFO - Wrote 5000 records to disk (total: 90000)
2025-07-18 14:14:58,592 - __main__ - INFO - Processing batch 19: 5000 records
2025-07-18 14:14:59,741 - __main__ - INFO - Wrote 5000 records to disk (total: 95000)
2025-07-18 14:15:02,900 - __main__ - INFO - Processing batch 20: 5000 records
2025-07-18 14:15:04,000 - __main__ - INFO - Wrote 5000 records to disk (total: 100000)
2025-07-18 14:15:04,145 - __main__ - INFO - GC freed 0.0MB after 100000 records
2025-07-18 14:15:07,790 - __main__ - INFO - Processing batch 21: 5000 records
2025-07-18 14:15:08,937 - __main__ - INFO - Wrote 5000 records to disk (total: 105000)
2025-07-18 14:15:12,229 - __main__ - INFO - Processing batch 22: 5000 records
2025-07-18 14:15:13,274 - __main__ - INFO - Wrote 5000 records to disk (total: 110000)
2025-07-18 14:15:16,622 - __main__ - INFO - Processing batch 23: 5000 records
2025-07-18 14:15:17,719 - __main__ - INFO - Wrote 5000 records to disk (total: 115000)
2025-07-18 14:15:21,249 - __main__ - INFO - Processing batch 24: 5000 records
2025-07-18 14:15:22,559 - __main__ - INFO - Wrote 5000 records to disk (total: 120000)
2025-07-18 14:15:26,427 - __main__ - INFO - Processing batch 25: 5000 records
2025-07-18 14:15:27,514 - __main__ - INFO - Wrote 5000 records to disk (total: 125000)
2025-07-18 14:15:31,489 - __main__ - INFO - Processing batch 26: 5000 records
2025-07-18 14:15:32,600 - __main__ - INFO - Wrote 5000 records to disk (total: 130000)
2025-07-18 14:15:36,296 - __main__ - INFO - Processing batch 27: 5000 records
2025-07-18 14:15:37,608 - __main__ - INFO - Wrote 5000 records to disk (total: 135000)
2025-07-18 14:15:41,961 - __main__ - INFO - Processing batch 28: 5000 records
2025-07-18 14:15:43,306 - __main__ - INFO - Wrote 5000 records to disk (total: 140000)
2025-07-18 14:15:47,147 - __main__ - INFO - Processing batch 29: 5000 records
2025-07-18 14:15:48,362 - __main__ - INFO - Wrote 5000 records to disk (total: 145000)
2025-07-18 14:15:52,239 - __main__ - INFO - Processing batch 30: 5000 records
2025-07-18 14:15:53,476 - __main__ - INFO - Wrote 5000 records to disk (total: 150000)
2025-07-18 14:15:53,657 - __main__ - INFO - GC freed 0.0MB after 150000 records
2025-07-18 14:15:57,652 - __main__ - INFO - Processing batch 31: 5000 records
2025-07-18 14:15:58,664 - __main__ - INFO - Wrote 5000 records to disk (total: 155000)
2025-07-18 14:16:02,440 - __main__ - INFO - Processing batch 32: 5000 records
2025-07-18 14:16:03,657 - __main__ - INFO - Wrote 5000 records to disk (total: 160000)
2025-07-18 14:16:07,502 - __main__ - INFO - Processing batch 33: 5000 records
2025-07-18 14:16:08,606 - __main__ - INFO - Wrote 5000 records to disk (total: 165000)
2025-07-18 14:16:12,516 - __main__ - INFO - Processing batch 34: 5000 records
2025-07-18 14:16:13,635 - __main__ - INFO - Wrote 5000 records to disk (total: 170000)
2025-07-18 14:16:17,556 - __main__ - INFO - Processing batch 35: 5000 records
2025-07-18 14:16:18,548 - __main__ - INFO - Wrote 5000 records to disk (total: 175000)
2025-07-18 14:16:22,567 - __main__ - INFO - Processing batch 36: 5000 records
2025-07-18 14:16:23,742 - __main__ - INFO - Wrote 5000 records to disk (total: 180000)
2025-07-18 14:16:27,848 - __main__ - INFO - Processing batch 37: 5000 records
2025-07-18 14:16:28,861 - __main__ - INFO - Wrote 5000 records to disk (total: 185000)
2025-07-18 14:16:33,216 - __main__ - INFO - Processing batch 38: 5000 records
2025-07-18 14:16:34,271 - __main__ - INFO - Wrote 5000 records to disk (total: 190000)
2025-07-18 14:16:38,625 - __main__ - INFO - Processing batch 39: 5000 records
2025-07-18 14:16:39,750 - __main__ - INFO - Wrote 5000 records to disk (total: 195000)
2025-07-18 14:16:44,174 - __main__ - INFO - Processing batch 40: 5000 records
2025-07-18 14:16:45,295 - __main__ - INFO - Wrote 5000 records to disk (total: 200000)
2025-07-18 14:16:45,442 - __main__ - INFO - GC freed 0.0MB after 200000 records
2025-07-18 14:16:49,841 - __main__ - INFO - Processing batch 41: 5000 records
2025-07-18 14:16:51,014 - __main__ - INFO - Wrote 5000 records to disk (total: 205000)
2025-07-18 14:16:55,724 - __main__ - INFO - Processing batch 42: 5000 records
2025-07-18 14:16:56,845 - __main__ - INFO - Wrote 5000 records to disk (total: 210000)
2025-07-18 14:17:01,489 - __main__ - INFO - Processing batch 43: 5000 records
2025-07-18 14:17:02,422 - __main__ - INFO - Wrote 5000 records to disk (total: 215000)
2025-07-18 14:17:06,989 - __main__ - INFO - Processing batch 44: 5000 records
2025-07-18 14:17:07,929 - __main__ - INFO - Wrote 5000 records to disk (total: 220000)
2025-07-18 14:17:12,490 - __main__ - INFO - Processing batch 45: 5000 records
2025-07-18 14:17:13,507 - __main__ - INFO - Wrote 5000 records to disk (total: 225000)
2025-07-18 14:17:18,105 - __main__ - INFO - Processing batch 46: 5000 records
2025-07-18 14:17:19,068 - __main__ - INFO - Wrote 5000 records to disk (total: 230000)
2025-07-18 14:17:23,806 - __main__ - INFO - Processing batch 47: 5000 records
2025-07-18 14:17:25,145 - __main__ - INFO - Wrote 5000 records to disk (total: 235000)
2025-07-18 14:17:30,152 - __main__ - INFO - Processing batch 48: 5000 records
2025-07-18 14:17:31,480 - __main__ - INFO - Wrote 5000 records to disk (total: 240000)
2025-07-18 14:17:39,841 - __main__ - INFO - Processing batch 49: 5000 records
2025-07-18 14:17:41,917 - __main__ - INFO - Wrote 5000 records to disk (total: 245000)
2025-07-18 14:17:47,400 - __main__ - INFO - Processing batch 50: 5000 records
2025-07-18 14:17:48,534 - __main__ - INFO - Wrote 5000 records to disk (total: 250000)
2025-07-18 14:17:48,779 - __main__ - INFO - GC freed 0.0MB after 250000 records
2025-07-18 14:17:53,768 - __main__ - INFO - Processing batch 51: 5000 records
2025-07-18 14:17:54,918 - __main__ - INFO - Wrote 5000 records to disk (total: 255000)
2025-07-18 14:18:00,145 - __main__ - INFO - Processing batch 52: 5000 records
2025-07-18 14:18:01,495 - __main__ - INFO - Wrote 5000 records to disk (total: 260000)
2025-07-18 14:18:06,912 - __main__ - INFO - Processing batch 53: 5000 records
2025-07-18 14:18:08,131 - __main__ - INFO - Wrote 5000 records to disk (total: 265000)
2025-07-18 14:18:14,168 - __main__ - INFO - Processing batch 54: 5000 records
2025-07-18 14:18:15,591 - __main__ - INFO - Wrote 5000 records to disk (total: 270000)
2025-07-18 14:18:21,471 - __main__ - INFO - Processing batch 55: 5000 records
2025-07-18 14:18:23,047 - __main__ - INFO - Wrote 5000 records to disk (total: 275000)
2025-07-18 14:18:28,717 - __main__ - INFO - Processing batch 56: 5000 records
2025-07-18 14:18:29,891 - __main__ - INFO - Wrote 5000 records to disk (total: 280000)
2025-07-18 14:18:35,139 - __main__ - INFO - Processing batch 57: 5000 records
2025-07-18 14:18:36,068 - __main__ - INFO - Wrote 5000 records to disk (total: 285000)
2025-07-18 14:18:41,716 - __main__ - INFO - Processing batch 58: 5000 records
2025-07-18 14:18:43,229 - __main__ - INFO - Wrote 5000 records to disk (total: 290000)
2025-07-18 14:18:48,781 - __main__ - INFO - Processing batch 59: 5000 records
2025-07-18 14:18:49,987 - __main__ - INFO - Wrote 5000 records to disk (total: 295000)
2025-07-18 14:18:55,576 - __main__ - INFO - Processing batch 60: 5000 records
2025-07-18 14:18:56,752 - __main__ - INFO - Wrote 5000 records to disk (total: 300000)
2025-07-18 14:18:56,906 - __main__ - INFO - GC freed 0.0MB after 300000 records
2025-07-18 14:19:02,516 - __main__ - INFO - Processing batch 61: 5000 records
2025-07-18 14:19:03,773 - __main__ - INFO - Wrote 5000 records to disk (total: 305000)
2025-07-18 14:19:09,781 - __main__ - INFO - Processing batch 62: 5000 records
2025-07-18 14:19:11,022 - __main__ - INFO - Wrote 5000 records to disk (total: 310000)
2025-07-18 14:19:17,222 - __main__ - INFO - Processing batch 63: 5000 records
2025-07-18 14:19:18,777 - __main__ - INFO - Wrote 5000 records to disk (total: 315000)
2025-07-18 14:19:24,415 - __main__ - INFO - Processing batch 64: 5000 records
2025-07-18 14:19:25,435 - __main__ - INFO - Wrote 5000 records to disk (total: 320000)
2025-07-18 14:19:31,241 - __main__ - INFO - Processing batch 65: 5000 records
2025-07-18 14:19:32,439 - __main__ - INFO - Wrote 5000 records to disk (total: 325000)
2025-07-18 14:19:38,168 - __main__ - INFO - Processing batch 66: 5000 records
2025-07-18 14:19:39,410 - __main__ - INFO - Wrote 5000 records to disk (total: 330000)
2025-07-18 14:19:47,341 - __main__ - INFO - Processing batch 67: 5000 records
2025-07-18 14:19:48,382 - __main__ - INFO - Wrote 5000 records to disk (total: 335000)
2025-07-18 14:19:54,716 - __main__ - INFO - Processing batch 68: 5000 records
2025-07-18 14:19:55,770 - __main__ - INFO - Wrote 5000 records to disk (total: 340000)
2025-07-18 14:20:02,735 - __main__ - INFO - Processing batch 69: 5000 records
2025-07-18 14:20:04,075 - __main__ - INFO - Wrote 5000 records to disk (total: 345000)
2025-07-18 14:20:10,550 - __main__ - INFO - Processing batch 70: 5000 records
2025-07-18 14:20:11,968 - __main__ - INFO - Wrote 5000 records to disk (total: 350000)
2025-07-18 14:20:12,132 - __main__ - INFO - GC freed 0.0MB after 350000 records
2025-07-18 14:20:19,068 - __main__ - INFO - Processing batch 71: 5000 records
2025-07-18 14:20:20,403 - __main__ - INFO - Wrote 5000 records to disk (total: 355000)
2025-07-18 14:20:28,102 - __main__ - INFO - Processing batch 72: 5000 records
2025-07-18 14:20:29,549 - __main__ - INFO - Wrote 5000 records to disk (total: 360000)
2025-07-18 14:20:36,557 - __main__ - INFO - Processing batch 73: 5000 records
2025-07-18 14:20:38,618 - __main__ - INFO - Wrote 5000 records to disk (total: 365000)
2025-07-18 14:20:45,651 - __main__ - INFO - Processing batch 74: 5000 records
2025-07-18 14:20:47,021 - __main__ - INFO - Wrote 5000 records to disk (total: 370000)
2025-07-18 14:20:54,344 - __main__ - INFO - Processing batch 75: 5000 records
2025-07-18 14:20:55,732 - __main__ - INFO - Wrote 5000 records to disk (total: 375000)
2025-07-18 14:21:02,750 - __main__ - INFO - Processing batch 76: 5000 records
2025-07-18 14:21:04,062 - __main__ - INFO - Wrote 5000 records to disk (total: 380000)
2025-07-18 14:21:11,336 - __main__ - INFO - Processing batch 77: 5000 records
2025-07-18 14:21:12,504 - __main__ - INFO - Wrote 5000 records to disk (total: 385000)
2025-07-18 14:21:19,929 - __main__ - INFO - Processing batch 78: 5000 records
2025-07-18 14:21:21,157 - __main__ - INFO - Wrote 5000 records to disk (total: 390000)
2025-07-18 14:21:28,003 - __main__ - INFO - Processing batch 79: 5000 records
2025-07-18 14:21:29,032 - __main__ - INFO - Wrote 5000 records to disk (total: 395000)
2025-07-18 14:21:36,038 - __main__ - INFO - Processing batch 80: 5000 records
2025-07-18 14:21:37,030 - __main__ - INFO - Wrote 5000 records to disk (total: 400000)
2025-07-18 14:21:37,196 - __main__ - INFO - GC freed 0.0MB after 400000 records
2025-07-18 14:21:44,067 - __main__ - INFO - Processing batch 81: 5000 records
2025-07-18 14:21:45,356 - __main__ - INFO - Wrote 5000 records to disk (total: 405000)
2025-07-18 14:21:52,631 - __main__ - INFO - Processing batch 82: 5000 records
2025-07-18 14:21:53,944 - __main__ - INFO - Wrote 5000 records to disk (total: 410000)
2025-07-18 14:22:01,361 - __main__ - INFO - Processing batch 83: 5000 records
2025-07-18 14:22:02,507 - __main__ - INFO - Wrote 5000 records to disk (total: 415000)
2025-07-18 14:22:10,317 - __main__ - INFO - Processing batch 84: 5000 records
2025-07-18 14:22:11,451 - __main__ - INFO - Wrote 5000 records to disk (total: 420000)
2025-07-18 14:22:20,398 - __main__ - INFO - Processing batch 85: 5000 records
2025-07-18 14:22:21,736 - __main__ - INFO - Wrote 5000 records to disk (total: 425000)
2025-07-18 14:22:29,477 - __main__ - INFO - Processing batch 86: 5000 records
2025-07-18 14:22:30,566 - __main__ - INFO - Wrote 5000 records to disk (total: 430000)
2025-07-18 14:22:38,081 - __main__ - INFO - Processing batch 87: 5000 records
2025-07-18 14:22:39,191 - __main__ - INFO - Wrote 5000 records to disk (total: 435000)
2025-07-18 14:22:47,755 - __main__ - INFO - Processing batch 88: 5000 records
2025-07-18 14:22:48,801 - __main__ - INFO - Wrote 5000 records to disk (total: 440000)
2025-07-18 14:22:57,606 - __main__ - INFO - Processing batch 89: 5000 records
2025-07-18 14:22:58,563 - __main__ - INFO - Wrote 5000 records to disk (total: 445000)
2025-07-18 14:23:06,253 - __main__ - INFO - Processing batch 90: 5000 records
2025-07-18 14:23:07,315 - __main__ - INFO - Wrote 5000 records to disk (total: 450000)
2025-07-18 14:23:07,466 - __main__ - INFO - GC freed 0.0MB after 450000 records
2025-07-18 14:23:15,611 - __main__ - INFO - Processing batch 91: 5000 records
2025-07-18 14:23:16,824 - __main__ - INFO - Wrote 5000 records to disk (total: 455000)
2025-07-18 14:23:25,180 - __main__ - INFO - Processing batch 92: 5000 records
2025-07-18 14:23:26,472 - __main__ - INFO - Wrote 5000 records to disk (total: 460000)
2025-07-18 14:23:37,101 - __main__ - INFO - Processing batch 93: 5000 records
2025-07-18 14:23:39,283 - __main__ - INFO - Wrote 5000 records to disk (total: 465000)
2025-07-18 14:23:47,642 - __main__ - INFO - Processing batch 94: 5000 records
2025-07-18 14:23:48,811 - __main__ - INFO - Wrote 5000 records to disk (total: 470000)
2025-07-18 14:23:56,498 - __main__ - INFO - Processing batch 95: 5000 records
2025-07-18 14:23:57,952 - __main__ - INFO - Wrote 5000 records to disk (total: 475000)
2025-07-18 14:24:05,916 - __main__ - INFO - Processing batch 96: 5000 records
2025-07-18 14:24:07,066 - __main__ - INFO - Wrote 5000 records to disk (total: 480000)
2025-07-18 14:24:15,214 - __main__ - INFO - Processing batch 97: 5000 records
2025-07-18 14:24:16,283 - __main__ - INFO - Wrote 5000 records to disk (total: 485000)
2025-07-18 14:24:24,301 - __main__ - INFO - Processing batch 98: 5000 records
2025-07-18 14:24:25,465 - __main__ - INFO - Wrote 5000 records to disk (total: 490000)
2025-07-18 14:24:33,567 - __main__ - INFO - Processing batch 99: 5000 records
2025-07-18 14:24:34,842 - __main__ - INFO - Wrote 5000 records to disk (total: 495000)
2025-07-18 14:24:43,215 - __main__ - INFO - Processing batch 100: 5000 records
2025-07-18 14:24:44,232 - __main__ - INFO - Wrote 5000 records to disk (total: 500000)
2025-07-18 14:24:44,372 - __main__ - INFO - GC freed 0.0MB after 500000 records
2025-07-18 14:24:52,455 - __main__ - INFO - Processing batch 101: 5000 records
2025-07-18 14:24:53,504 - __main__ - INFO - Wrote 5000 records to disk (total: 505000)
2025-07-18 14:25:01,909 - __main__ - INFO - Processing batch 102: 5000 records
2025-07-18 14:25:02,959 - __main__ - INFO - Wrote 5000 records to disk (total: 510000)
2025-07-18 14:25:11,521 - __main__ - INFO - Processing batch 103: 5000 records
2025-07-18 14:25:12,584 - __main__ - INFO - Wrote 5000 records to disk (total: 515000)
2025-07-18 14:25:21,820 - __main__ - INFO - Processing batch 104: 5000 records
2025-07-18 14:25:22,801 - __main__ - INFO - Wrote 5000 records to disk (total: 520000)
2025-07-18 14:25:31,547 - __main__ - INFO - Processing batch 105: 5000 records
2025-07-18 14:25:32,716 - __main__ - INFO - Wrote 5000 records to disk (total: 525000)
2025-07-18 14:25:41,892 - __main__ - INFO - Processing batch 106: 5000 records
2025-07-18 14:25:43,334 - __main__ - INFO - Wrote 5000 records to disk (total: 530000)
2025-07-18 14:25:52,372 - __main__ - INFO - Processing batch 107: 5000 records
2025-07-18 14:25:53,492 - __main__ - INFO - Wrote 5000 records to disk (total: 535000)
2025-07-18 14:26:02,509 - __main__ - INFO - Processing batch 108: 5000 records
2025-07-18 14:26:03,868 - __main__ - INFO - Wrote 5000 records to disk (total: 540000)
2025-07-18 14:26:13,155 - __main__ - INFO - Processing batch 109: 5000 records
2025-07-18 14:26:14,382 - __main__ - INFO - Wrote 5000 records to disk (total: 545000)
2025-07-18 14:26:23,297 - __main__ - INFO - Processing batch 110: 5000 records
2025-07-18 14:26:24,343 - __main__ - INFO - Wrote 5000 records to disk (total: 550000)
2025-07-18 14:26:24,534 - __main__ - INFO - GC freed 0.0MB after 550000 records
2025-07-18 14:26:33,533 - __main__ - INFO - Processing batch 111: 5000 records
2025-07-18 14:26:34,646 - __main__ - INFO - Wrote 5000 records to disk (total: 555000)
2025-07-18 14:26:43,774 - __main__ - INFO - Processing batch 112: 5000 records
2025-07-18 14:26:44,812 - __main__ - INFO - Wrote 5000 records to disk (total: 560000)
2025-07-18 14:26:54,019 - __main__ - INFO - Processing batch 113: 5000 records
2025-07-18 14:26:55,168 - __main__ - INFO - Wrote 5000 records to disk (total: 565000)
2025-07-18 14:27:04,484 - __main__ - INFO - Processing batch 114: 5000 records
2025-07-18 14:27:05,614 - __main__ - INFO - Wrote 5000 records to disk (total: 570000)
2025-07-18 14:27:14,781 - __main__ - INFO - Processing batch 115: 5000 records
2025-07-18 14:27:15,785 - __main__ - INFO - Wrote 5000 records to disk (total: 575000)
2025-07-18 14:27:24,956 - __main__ - INFO - Processing batch 116: 5000 records
2025-07-18 14:27:26,026 - __main__ - INFO - Wrote 5000 records to disk (total: 580000)
2025-07-18 14:27:35,507 - __main__ - INFO - Processing batch 117: 5000 records
2025-07-18 14:27:36,628 - __main__ - INFO - Wrote 5000 records to disk (total: 585000)
2025-07-18 14:27:45,765 - __main__ - INFO - Processing batch 118: 5000 records
2025-07-18 14:27:46,827 - __main__ - INFO - Wrote 5000 records to disk (total: 590000)
2025-07-18 14:27:56,202 - __main__ - INFO - Processing batch 119: 5000 records
2025-07-18 14:27:57,328 - __main__ - INFO - Wrote 5000 records to disk (total: 595000)
2025-07-18 14:28:07,672 - __main__ - INFO - Processing batch 120: 5000 records
2025-07-18 14:28:09,358 - __main__ - INFO - Wrote 5000 records to disk (total: 600000)
2025-07-18 14:28:09,543 - __main__ - INFO - GC freed 0.0MB after 600000 records
2025-07-18 14:28:19,530 - __main__ - INFO - Processing batch 121: 5000 records
2025-07-18 14:28:20,812 - __main__ - INFO - Wrote 5000 records to disk (total: 605000)
2025-07-18 14:28:30,487 - __main__ - INFO - Processing batch 122: 5000 records
2025-07-18 14:28:31,849 - __main__ - INFO - Wrote 5000 records to disk (total: 610000)
2025-07-18 14:28:41,504 - __main__ - INFO - Processing batch 123: 5000 records
2025-07-18 14:28:42,570 - __main__ - INFO - Wrote 5000 records to disk (total: 615000)
2025-07-18 14:28:52,308 - __main__ - INFO - Processing batch 124: 5000 records
2025-07-18 14:28:53,490 - __main__ - INFO - Wrote 5000 records to disk (total: 620000)
2025-07-18 14:29:03,739 - __main__ - INFO - Processing batch 125: 5000 records
2025-07-18 14:29:05,144 - __main__ - INFO - Wrote 5000 records to disk (total: 625000)
2025-07-18 14:29:15,330 - __main__ - INFO - Processing batch 126: 5000 records
2025-07-18 14:29:16,753 - __main__ - INFO - Wrote 5000 records to disk (total: 630000)
2025-07-18 14:29:26,830 - __main__ - INFO - Processing batch 127: 5000 records
2025-07-18 14:29:27,944 - __main__ - INFO - Wrote 5000 records to disk (total: 635000)
2025-07-18 14:29:37,917 - __main__ - INFO - Processing batch 128: 5000 records
2025-07-18 14:29:39,023 - __main__ - INFO - Wrote 5000 records to disk (total: 640000)
2025-07-18 14:29:49,003 - __main__ - INFO - Processing batch 129: 5000 records
2025-07-18 14:29:50,231 - __main__ - INFO - Wrote 5000 records to disk (total: 645000)
2025-07-18 14:30:00,021 - __main__ - INFO - Processing batch 130: 5000 records
2025-07-18 14:30:01,141 - __main__ - INFO - Wrote 5000 records to disk (total: 650000)
2025-07-18 14:30:01,327 - __main__ - INFO - GC freed 0.0MB after 650000 records
2025-07-18 14:30:11,590 - __main__ - INFO - Processing batch 131: 5000 records
2025-07-18 14:30:12,618 - __main__ - INFO - Wrote 5000 records to disk (total: 655000)
2025-07-18 14:30:23,087 - __main__ - INFO - Processing batch 132: 5000 records
2025-07-18 14:30:24,670 - __main__ - INFO - Wrote 5000 records to disk (total: 660000)
2025-07-18 14:30:35,431 - __main__ - INFO - Processing batch 133: 5000 records
2025-07-18 14:30:36,810 - __main__ - INFO - Wrote 5000 records to disk (total: 665000)
2025-07-18 14:30:47,780 - __main__ - INFO - Processing batch 134: 5000 records
2025-07-18 14:30:48,976 - __main__ - INFO - Wrote 5000 records to disk (total: 670000)
2025-07-18 14:30:59,385 - __main__ - INFO - Processing batch 135: 5000 records
2025-07-18 14:31:00,482 - __main__ - INFO - Wrote 5000 records to disk (total: 675000)
2025-07-18 14:31:11,153 - __main__ - INFO - Processing batch 136: 5000 records
2025-07-18 14:31:12,373 - __main__ - INFO - Wrote 5000 records to disk (total: 680000)
2025-07-18 14:31:22,852 - __main__ - INFO - Processing batch 137: 5000 records
2025-07-18 14:31:23,876 - __main__ - INFO - Wrote 5000 records to disk (total: 685000)
2025-07-18 14:31:34,288 - __main__ - INFO - Processing batch 138: 5000 records
2025-07-18 14:31:35,401 - __main__ - INFO - Wrote 5000 records to disk (total: 690000)
2025-07-18 14:31:46,095 - __main__ - INFO - Processing batch 139: 5000 records
2025-07-18 14:31:47,158 - __main__ - INFO - Wrote 5000 records to disk (total: 695000)
2025-07-18 14:31:57,780 - __main__ - INFO - Processing batch 140: 5000 records
2025-07-18 14:31:58,875 - __main__ - INFO - Wrote 5000 records to disk (total: 700000)
2025-07-18 14:31:59,017 - __main__ - INFO - GC freed 0.0MB after 700000 records
2025-07-18 14:32:08,322 - __main__ - INFO - Processing batch 141: 1370 records
2025-07-18 14:32:08,645 - __main__ - INFO - Wrote 1370 records to disk (total: 701370)
2025-07-18 14:32:08,660 - __main__ - INFO - Processed 701370 records, converting to numpy arrays...
2025-07-18 14:32:09,607 - __main__ - INFO - Cache built successfully: 701370 records
2025-07-18 14:32:09,607 - __main__ - INFO - Embeddings shape: (701370, 384)
2025-07-18 14:32:09,611 - __main__ - INFO - Disk usage: 310.4MB
2025-07-18 14:32:09,658 - __main__ - INFO - Loading memory-mapped cache...
2025-07-18 14:32:09,720 - __main__ - INFO - Cache loaded: 701370 records
2025-07-18 14:32:09,722 - __main__ - INFO - Disk usage: 310.4MB
2025-07-18 14:32:09,723 - __main__ - INFO - RAM usage: ~10.0MB
2025-07-18 14:32:09,723 - __main__ - INFO - Memory-mapped cache ready: 701370 records
2025-07-18 14:32:09,726 - __main__ - INFO - Disk usage: 310.4MB
2025-07-18 14:32:09,727 - __main__ - INFO - RAM usage: ~10.0MB
2025-07-18 14:32:09,727 - __main__ - INFO - Memory-mapped cache loaded: 701370 records
2025-07-18 14:34:16,097 - __main__ - INFO - Request 1752863656-2126872764224: GET http://localhost:8000/search?q=breakin&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-18 14:34:16,100 - __main__ - INFO - Processing search request: 'breakin' on table 'ReportNLP'
2025-07-18 14:34:16,100 - __main__ - INFO - Memory-mapped search: 'breakin'
2025-07-18 14:34:38,932 - __main__ - INFO - Memory-mapped search completed: 0 results in 22831.45ms
2025-07-18 14:34:38,937 - __main__ - WARNING - Request 1752863656-2126872764224 increased memory by 304.6MB (from 463.0MB to 767.6MB)
2025-07-18 14:34:39,086 - __main__ - INFO - Request 1752863656-2126872764224 completed in 22840.44ms with status 200
2025-07-18 14:38:46,473 - __main__ - INFO - Shutting down Search API...
2025-07-18 14:38:46,476 - __main__ - INFO - Database connection closed
2025-07-18 15:02:39,190 - __main__ - INFO - Starting up Search API...
2025-07-18 15:02:39,191 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-18 15:02:39,194 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 15:02:39,195 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-18 15:02:42,174 - __main__ - INFO - Model loaded successfully
2025-07-18 15:02:42,174 - __main__ - INFO - Initializing database connection...
2025-07-18 15:02:42,278 - __main__ - INFO - Database connection established
2025-07-18 15:02:42,278 - __main__ - INFO - No cache file found or failed to load: [Errno 2] No such file or directory: 'search_cache\\embeddings.npy'. Building cache...
2025-07-18 15:02:42,279 - __main__ - INFO - Loading search data with ConnectorX...
2025-07-18 15:02:42,279 - __main__ - INFO - Fetching data with ConnectorX...
2025-07-21 13:09:07,706 - __main__ - INFO - Starting up Search API...
2025-07-21 13:09:07,707 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-21 13:09:07,711 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-21 13:09:07,711 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-21 13:09:10,844 - __main__ - INFO - Model loaded successfully
2025-07-21 13:09:10,845 - __main__ - INFO - Initializing database connection...
2025-07-21 13:09:10,979 - __main__ - INFO - Database connection established
2025-07-21 13:09:10,980 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-21 13:09:10,980 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-21 13:09:10,980 - __main__ - INFO - Building BM25 index...
2025-07-21 13:09:10,981 - __main__ - INFO - Building disk-backed BM25 index...
2025-07-21 13:09:10,981 - __main__ - INFO - Starting text chunk generation for BM25...
2025-07-21 13:09:11,702 - __main__ - INFO - Total documents for BM25: 75513
2025-07-21 13:09:11,703 - __main__ - INFO - Loading text chunk: offset 0, size 50000
2025-07-21 13:09:14,071 - __main__ - INFO - Processing BM25 chunk 1: 50000 documents
